import{E as S}from"./jspdf.es.min-CEEO7WE_.js";import"./jspdf.plugin.autotable-Bc4jgkkc.js";import{c as l,a as r,t as d,m as g,s as C,F as c,p as y,v as b,q as u,k as m,o,n as v}from"./vendor-Xnl27S3x.js";import{_ as D}from"./index-Br_OsRMh.js";const F={data(){return{apiKey:"",filters:{acara:"",lokasi:"",tanggal:"",data:"",ranah:"",detailRanah:""},kelompokList:[],attendanceData:[],sortKey:"",sortOrder:"asc",sortCriteria:[],isLoading:!1}},computed:{uniqueRanah(){return[...new Set(this.kelompokList.map(e=>e.ranah))].sort()},mergedData(){const e={};for(const t of this.kelompokList){const i="".concat(t.ranah,"|").concat(t.detail_ranah);e[i]={ranah:t.ranah,detail_ranah:t.detail_ranah,hadir:"-",jam_hadir:"belum-hadir",nama:[]}}for(const t of this.attendanceData){const i="".concat(t.ranah,"|").concat(t.detail_ranah);e[i]&&(e[i].hadir==="-"?(e[i].hadir=t.nama,e[i].nama=[t.nama]):(e[i].nama.push(t.nama),e[i].hadir=e[i].nama.join(", ")),e[i].jam_hadir==="belum-hadir"&&(e[i].jam_hadir=t.jam_hadir))}return Object.values(e)},filteredAndSortedData(){let e=this.mergedData;return this.filters.ranah&&(e=e.filter(t=>t.ranah===this.filters.ranah)),this.filters.detailRanah&&(e=e.filter(t=>t.detail_ranah.toLowerCase().includes(this.filters.detailRanah.toLowerCase()))),this.sortCriteria.length===0?e:[...e].sort((t,i)=>{const h=t.hadir==="-",s=i.hadir==="-";if(h!==s)return h?1:-1;for(const n of this.sortCriteria){const a=n.key,f=n.order,p=(t[a]||"").toLowerCase(),k=(i[a]||"").toLowerCase();if(p!==k)return p<k?f==="asc"?-1:1:f==="asc"?1:-1}return 0})},totalHadir(){return this.filteredAndSortedData.filter(e=>e.hadir!=="-").length},totalBelumHadir(){return this.filteredAndSortedData.filter(e=>e.hadir==="-").length}},methods:{formatTanggalIndo(e){const t=new Date(e),i=["Minggu","Senin","Selasa","Rabu","Kamis","Jumat","Sabtu"],h=["Januari","Februari","Maret","April","Mei","Juni","Juli","Agustus","September","Oktober","November","Desember"];return"".concat(i[t.getDay()],", ").concat(t.getDate()," ").concat(h[t.getMonth()]," ").concat(t.getFullYear())},async refresh(){this.isLoading=!0;try{await Promise.all([this.fetchKelompok(),this.fetchAttendance()])}finally{this.isLoading=!1}},async fetchKelompok(){const e="/api/data/daerah/".concat(encodeURIComponent(this.filters.data)),t=await fetch(e,{headers:{Authorization:"ApiKey ".concat(this.apiKey)}});if(!t.ok)throw new Error("Gagal fetch kelompok");const i=await t.json();this.kelompokList=i},async fetchAttendance(){const e="/api/absen-pengajian/?tanggal=".concat(this.filters.tanggal,"&acara=").concat(encodeURIComponent(this.filters.acara),"&lokasi=").concat(encodeURIComponent(this.filters.lokasi)),t=await fetch(e,{headers:{Authorization:"ApiKey ".concat(this.apiKey)}});if(!t.ok)throw new Error("Gagal fetch absen");this.attendanceData=await t.json()},handleSort(e){const t=this.sortCriteria.findIndex(i=>i.key===e);if(t!==-1){const i=this.sortCriteria[t].order;this.sortCriteria[t].order=i==="asc"?"desc":"asc"}else this.sortCriteria.push({key:e,order:"asc"});this.sortKey=e,this.sortOrder=this.sortCriteria.find(i=>i.key===e).order},filterTable(){},downloadPDF(){const e=new S({unit:"cm",format:"a4"});e.setFont("times","normal"),e.setFontSize(18),e.text("Daftar Pantauan Absen Kelompok",e.internal.pageSize.getWidth()/2,2,{align:"center"}),e.setFontSize(12),e.text("Acara: ".concat(this.filters.acara||"UMUM"),e.internal.pageSize.getWidth()/2,2.8,{align:"center"}),e.text("Lokasi: ".concat(this.filters.lokasi||"-"),e.internal.pageSize.getWidth()/2,3.3,{align:"center"}),e.text("Tanggal: ".concat(this.formatTanggalIndo(this.filters.tanggal)),e.internal.pageSize.getWidth()/2,3.8,{align:"center"}),e.autoTable({head:[["No","Desa","Kelompok","Hadir","Jam Hadir"]],body:this.filteredAndSortedData.map((i,h)=>[h+1,i.ranah,i.detail_ranah,i.hadir,i.jam_hadir]),startY:4.8,styles:{fontSize:10,cellPadding:.4},didDrawPage:i=>{const h=e.internal.pageSize.height,s=e.internal.pageSize.width;e.setFontSize(9),e.setTextColor(128),e.text("Hal ".concat(e.internal.getNumberOfPages()),s-1,h-.8,{align:"right"})}});const t=["PantauAbsen",this.filters.lokasi||"ALL",this.filters.acara||"UMUM",this.filters.tanggal||""].join("-");e.save("".concat(t,".pdf"))},handleTanggalChange(){this.refresh()}},watch:{"filters.acara"(e){document.title="Pantauan Per Kelompok / Bidang - ".concat(e||"UMUM")}},mounted(){const e=new URLSearchParams(window.location.search);this.apiKey=e.get("key")||"",this.filters.acara=e.get("acara")||"",this.filters.lokasi=e.get("lokasi")||"",this.filters.data=e.get("data")||"";const t=new Date;this.filters.tanggal="".concat(t.getFullYear(),"-").concat(String(t.getMonth()+1).padStart(2,"0"),"-").concat(String(t.getDate()).padStart(2,"0")),this.refresh(),document.title="Pantauan Per Kelompok / Bidang - ".concat(this.filters.acara||"UMUM")}},K={id:"pantau-ngaji-cek"},_={style:{"margin-bottom":"10px","text-align":"center","font-weight":"bold"}},x={class:"filter-item"},A=["value"],M={key:0,class:"table-container"},P={class:"summary-container"},T={class:"summary-item"},L={class:"summary-value"},w={class:"summary-item"},U={class:"summary-value"},j={key:0},R={key:0},z={key:0},H={key:0},I={key:0},O={key:1,class:"danger-text"},B={key:0},E={key:1,class:"danger-text"},N={key:1,style:{"text-align":"center",padding:"2em"}},V={class:"button-container"};function J(e,t,i,h,s,n){return o(),l("div",K,[t[22]||(t[22]=r("h1",null,"-- VERSI BETA RELEASE --",-1)),t[23]||(t[23]=r("h1",null,"Pantauan Absen Per Kelompok",-1)),r("div",_,d(s.filters.acara||"UMUM"),1),r("section",null,[r("div",x,[r("div",null,[t[13]||(t[13]=r("label",{for:"desaFilter"},"Filter Desa:",-1)),g(r("select",{"onUpdate:modelValue":t[0]||(t[0]=a=>s.filters.ranah=a),onChange:t[1]||(t[1]=(...a)=>n.filterTable&&n.filterTable(...a)),id:"desaFilter"},[t[12]||(t[12]=r("option",{value:""},"Semua",-1)),(o(!0),l(c,null,y(n.uniqueRanah,a=>(o(),l("option",{key:a,value:a},d(a),9,A))),128))],544),[[C,s.filters.ranah]])]),r("div",null,[t[14]||(t[14]=r("label",{for:"tanggalFilter"},"Filter Tanggal:",-1)),g(r("input",{type:"date","onUpdate:modelValue":t[2]||(t[2]=a=>s.filters.tanggal=a),onInput:t[3]||(t[3]=(...a)=>n.handleTanggalChange&&n.handleTanggalChange(...a)),id:"tanggalFilter"},null,544),[[b,s.filters.tanggal]])]),r("div",null,[t[15]||(t[15]=r("label",{for:"kelompokFilter"},"Filter Kelompok:",-1)),g(r("input",{type:"text","onUpdate:modelValue":t[4]||(t[4]=a=>s.filters.detailRanah=a),onInput:t[5]||(t[5]=(...a)=>n.filterTable&&n.filterTable(...a)),placeholder:"Cari kelompok...",id:"kelompokFilter"},null,544),[[b,s.filters.detailRanah]])])])]),s.isLoading?(o(),l("div",N,"Memuat data...")):(o(),l("div",M,[r("div",P,[r("div",T,[t[16]||(t[16]=r("span",{class:"summary-label"},"Total Hadir:",-1)),r("span",L,d(n.totalHadir),1)]),r("div",w,[t[17]||(t[17]=r("span",{class:"summary-label"},"Total Belum Hadir:",-1)),r("span",U,d(n.totalBelumHadir),1)])]),r("table",null,[r("thead",null,[r("tr",null,[r("th",{onClick:t[6]||(t[6]=a=>n.handleSort("ranah"))},[t[18]||(t[18]=u(" Desa ",-1)),s.sortKey==="ranah"?(o(),l("span",j,d(s.sortOrder==="asc"?"↑":"↓"),1)):m("",!0)]),r("th",{onClick:t[7]||(t[7]=a=>n.handleSort("detail_ranah"))},[t[19]||(t[19]=u(" Kelompok ",-1)),s.sortKey==="detail_ranah"?(o(),l("span",R,d(s.sortOrder==="asc"?"↑":"↓"),1)):m("",!0)]),r("th",{onClick:t[8]||(t[8]=a=>n.handleSort("hadir"))},[t[20]||(t[20]=u(" Hadir ",-1)),s.sortKey==="hadir"?(o(),l("span",z,d(s.sortOrder==="asc"?"↑":"↓"),1)):m("",!0)]),r("th",{onClick:t[9]||(t[9]=a=>n.handleSort("jam_hadir"))},[t[21]||(t[21]=u(" Jam Hadir ",-1)),s.sortKey==="jam_hadir"?(o(),l("span",H,d(s.sortOrder==="asc"?"↑":"↓"),1)):m("",!0)])])]),r("tbody",null,[(o(!0),l(c,null,y(n.filteredAndSortedData,a=>(o(),l("tr",{key:a.ranah+"-"+a.detail_ranah,class:v({hadir:a.hadir!=="-","belum-hadir":a.hadir==="-"})},[r("td",null,d(a.ranah),1),r("td",null,d(a.detail_ranah),1),r("td",null,[a.hadir!=="-"?(o(),l("span",I,d(a.hadir),1)):(o(),l("span",O,"-"))]),r("td",null,[a.hadir!=="-"?(o(),l("span",B,d(a.jam_hadir),1)):(o(),l("span",E,"belum-hadir"))])],2))),128))])])])),r("div",V,[r("button",{onClick:t[10]||(t[10]=(...a)=>n.refresh&&n.refresh(...a))},"Refresh"),r("button",{onClick:t[11]||(t[11]=(...a)=>n.downloadPDF&&n.downloadPDF(...a))},"Download PDF")])])}const Q=D(F,[["render",J],["__scopeId","data-v-12b73d99"]]);export{Q as default};
