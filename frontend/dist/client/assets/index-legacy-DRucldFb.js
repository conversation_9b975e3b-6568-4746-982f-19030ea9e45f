System.register(["./vendor-legacy-BmDVBcfe.js"],function(a,t){"use strict";var e,n,i,s,r,o,c,l,m,d,u,p,g;return{setters:[a=>{e=a.d,n=a.c,i=a.o,s=a.a,r=a.b,o=a.w,c=a.r,l=a.e,m=a.f,d=a.g,u=a.h,p=a.i,g=a.j}],execute:function(){var h=document.createElement("style");h.textContent=".app,.main{margin:0;padding:0;border:0;font-size:100%;font:inherit;line-height:1;box-sizing:border-box;background-color:#fff;color:#2c4a3e;width:100%;display:flex;flex-direction:column;align-items:center;min-height:100vh}.loading{display:flex;justify-content:center;align-items:center;height:100vh;font-size:1.5rem}.view-wrapper{width:100%;max-width:100%;display:flex;flex-direction:column;align-items:center}\n/*$vite$:1*/",document.head.appendChild(h);const j=a("_",(a,t)=>{const e=a.__vccOpts||a;for(const[n,i]of t)e[n]=i;return e}),v=e({data:()=>({isLoading:!1}),computed:{currentRoute(){return this.$route}},methods:{setLoading(a){this.isLoading=a}}}),f={class:"app"},A={class:"main",role:"main"},y={key:1,class:"loading"},b=j(v,[["render",function(a,t,e,d,u,p){const g=c("router-view");return i(),n("div",f,[s("main",A,[r(g,null,{default:o(({Component:t,route:e})=>[(i(),n("div",{class:"view-wrapper",key:e.path},[a.isLoading?(i(),n("div",y,"Loading...")):(i(),l(m(t),{key:0,onLoading:a.setLoading},null,40,["onLoading"]))]))]),_:1})])])}]]),w=a("a",function(a,t,e){let n=Promise.resolve();function i(a){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=a,window.dispatchEvent(t),!t.defaultPrevented)throw a}return n.then(t=>{for(const a of t||[])"rejected"===a.status&&i(a.reason);return a().catch(i)})}),P={Ngaji:()=>w(()=>t.import("./ngaji-legacy-D61fab0q.js"),void 0),PantauNgaji:()=>w(()=>t.import("./pantau-ngaji-legacy-6mDVIB8n.js"),void 0),PantauNgajiCek:()=>w(()=>t.import("./pantau-ngaji-cek-legacy-J9bHC0-v.js"),void 0),StatNgaji:()=>w(()=>t.import("./stat-ngaji-legacy-jjquDCG5.js"),void 0),Asrama:()=>w(()=>t.import("./asrama-legacy-BbK6jeZV.js"),void 0),PantauAsrama:()=>w(()=>t.import("./pantau-asrama-legacy-C5ZqMRiX.js"),void 0),StatAsrama:()=>w(()=>t.import("./stat-asrama-legacy-C9OJpKjO.js"),void 0),Util:()=>w(()=>t.import("./util-legacy-BB1dU9Xl.js"),void 0)},k=[{path:"/ngaji",name:"Ngaji",title:"Absensi Acara"},{path:"/pantau-ngaji",name:"PantauNgaji",title:"Pantauan Absensi Acara"},{path:"/pantau-ngaji-cek",name:"PantauNgajiCek",title:"Pantauan Absen Acara"},{path:"/stat-ngaji",name:"StatNgaji",title:"Statistik Absensi Acara"},{path:"/asrama",name:"Asrama",title:"Absensi Asramaan"},{path:"/pantau-asrama",name:"PantauAsrama",title:"Pantauan Absensi Asramaan"},{path:"/stat-asrama",name:"StatAsrama",title:"Statistik Absensi Asramaan"},{path:"/",name:"Util",title:"Utilitas"}].map(a=>{const{name:t,path:e}=a;return{name:t,path:e,component:P[t],meta:{title:a.title,requiresAuth:!1}}}),x=d({history:u(),routes:k}),L=p(b),N=g();L.use(N),L.use(x),L.mount("#app")}}});
