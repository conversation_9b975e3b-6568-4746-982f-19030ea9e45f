const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/jspdf.es.min-CEEO7WE_.js","assets/index-Br_OsRMh.js","assets/vendor-Xnl27S3x.js","assets/index-CCdYf6ya.css"])))=>i.map(i=>d[i]);
import{_ as $,a as V}from"./index-Br_OsRMh.js";import{c as d,a as t,k,m as g,v as T,u as J,t as p,q as M,s as E,x as Q,F as G,p as I,n as B,o as u,y as Y}from"./vendor-Xnl27S3x.js";const ee={name:"Util",computed:{isValidUrl(){if(!this.qrGenerator.url)return!1;const a=this.qrGenerator.url,e=a.includes("/ngaji?")||a.includes("/ngaji")||a.endsWith("/ngaji"),o=a.includes("/asrama?")||a.includes("/asrama")||a.endsWith("/asrama");return console.log("🔍 isValidUrl check for:",a),console.log("🔍 isNgajiUrl:",e),console.log("🔍 isAsramaUrl:",o),e||o},hasAbsenUrl(){if(!this.qrGenerator.url)return!1;const a=this.qrGenerator.url,e=a.includes("/ngaji")||a.includes("/asrama"),o=a.includes("/pantau")||a.includes("/stat");return e&&!o},canGenerateUrls(){return!0}},data(){return{STORAGE_KEY:"util_auth",auth:{isLoggedIn:!1,isLoggingIn:!1,username:"",password:"",accessToken:"",loginError:""},apiKeys:[],defaultReadKey:null,defaultWriteKey:null,apiKeyForm:{name:"",expiresInDays:"30",isCreating:!1},daerahOptions:[{value:"medan-barat-tex",text:"Tex"},{value:"medan-barat-ppg",text:"Materi PPG"},{value:"medan-barat-smb",text:"Umum"},{value:"medan-barat-mbg",text:"MT/MS"}],sesiOptions:[{value:"asrama-normal",text:"Pagi Siang Malam"},{value:"asrama-padat",text:"Subuh Pagi Siang Malam"},{value:"mtms-medan-barat",text:"MT/MS"}],urlGenerator:{type:"acara",extraParams:{acara:"",lokasi:"",data:"",ph:"",sesi:"",tanggal:"",time:""}},showGeneratedUrls:!1,copyStatus:[!1,!1,!1],qrGenerator:{url:"",size:"300",customText:""},qrCodeUrl:null,urlShortener:{targetType:"absen",longUrl:"",selectedUrls:[]},shortenedUrls:[],isShortening:!1,shortUrlCopied:[],loggedApiKeyUsage:{writeKey:!1,readKey:!1,writeKeyWarning:!1,readKeyWarning:!1},domWarningShown:{writeKeyWarning:!1,readKeyWarning:!1},dataManager:{selectedEndpoint:"",parameter:"",data:[],isLoading:!1,error:null},apiTester:{selectedEndpoint:"",method:"GET",testDataJson:"",queryParams:"",response:null,isLoading:!1,error:null},additionalTools:{manualUrl:"",qrSize:"300",shortenedManualUrl:"",isProcessing:!1,qrCodeUrl:null,customText:""},baseUrl:window.location.origin}},computed:{showAttendanceParams(){return this.urlGenerator.type==="acara"||this.urlGenerator.type==="asrama"},showMonitoringParams(){return this.urlGenerator.type==="acara"||this.urlGenerator.type==="asrama"},showStatisticsParams(){return this.urlGenerator.type==="acara"||this.urlGenerator.type==="asrama"},showAsramaParams(){return this.urlGenerator.type==="asrama"},generatedUrls(){if(!this.urlGenerator.type)return[];const a=this.baseUrl,e=this.urlGenerator.type,o=[];return e==="acara"?(o.push({label:"URL Absensi Acara",url:this.buildUrl(a+"/ngaji","attendance")}),o.push({label:"URL Pantauan Acara",url:this.buildUrl(a+"/pantau-ngaji","monitoring")}),o.push({label:"URL Statistik Acara",url:this.buildUrl(a+"/stat-ngaji","statistics")})):e==="asrama"&&(o.push({label:"URL Absensi Asrama",url:this.buildUrl(a+"/asrama","attendance")}),o.push({label:"URL Pantauan Asrama",url:this.buildUrl(a+"/pantau-asrama","monitoring")}),o.push({label:"URL Statistik Asrama",url:this.buildUrl(a+"/stat-asrama","statistics")})),o},generatedUrl(){return this.generatedUrls.length>0?this.generatedUrls[0].url:""},validApiKeys(){if(!Array.isArray(this.apiKeys))return[];const a=new Date;return this.apiKeys.filter(e=>e.expires_at?new Date(e.expires_at)>a:!0)},expiredApiKeys(){if(!Array.isArray(this.apiKeys))return[];const a=new Date;return this.apiKeys.filter(e=>e.expires_at?new Date(e.expires_at)<=a:!1)}},watch:{generatedUrl(a){a&&!this.qrGenerator.url&&(this.qrGenerator.url=a)}},methods:{async login(){if(!(!this.auth.username||!this.auth.password)){this.auth.isLoggingIn=!0,this.auth.loginError="";try{const a=await fetch("/api/auth/login/",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:this.auth.username,password:this.auth.password})});if(!a.ok){let o="Login gagal";try{o=(await a.json()).detail||o}catch(n){o="Server error (".concat(a.status,"): ").concat(a.statusText)}throw new Error(o)}const e=await a.json();this.auth.accessToken=e.access,this.auth.isLoggedIn=!0,this.auth.password="",localStorage.setItem(this.STORAGE_KEY,JSON.stringify({username:this.auth.username,accessToken:this.auth.accessToken})),await this.loadApiKeys()}catch(a){console.error("Login error:",a),this.auth.loginError=a.message||"Gagal login. Periksa username dan password Anda."}finally{this.auth.isLoggingIn=!1}}},logout(){localStorage.removeItem(this.STORAGE_KEY),this.auth={isLoggedIn:!1,isLoggingIn:!1,username:"",password:"",accessToken:"",loginError:""},this.apiKeys=[],this.apiKeyForm={name:"",expiresInDays:"30",isCreating:!1}},isDefault(a){return this.defaultReadKey&&this.defaultReadKey.id===a.id||this.defaultWriteKey&&this.defaultWriteKey.id===a.id},setAsDefault(a){a.permission==="read_only"&&(this.defaultReadKey=a,console.log('✅ Set "'.concat(a.name,'" as default READ_ONLY key')),this.loggedApiKeyUsage.readKey=!1,this.loggedApiKeyUsage.readKeyWarning=!1,this.domWarningShown.readKeyWarning=!1),a.permission==="write_only"&&(this.defaultWriteKey=a,console.log('✅ Set "'.concat(a.name,'" as default WRITE_ONLY key')),this.loggedApiKeyUsage.writeKey=!1,this.loggedApiKeyUsage.writeKeyWarning=!1,this.domWarningShown.writeKeyWarning=!1),localStorage.setItem("defaultReadKey",JSON.stringify(this.defaultReadKey)),localStorage.setItem("defaultWriteKey",JSON.stringify(this.defaultWriteKey));const e=a.permission.replace("_"," ").toUpperCase();alert('✅ "'.concat(a.name,'" telah dijadikan default ').concat(e," key"))},getKeyTypeForUrl(a){return a.includes("Absensi")?"write_only":(a.includes("Pantauan")||a.includes("Statistik"),"read_only")},getKeyTypeBadge(a){const e=this.getKeyTypeForUrl(a);return{type:e,color:e==="write_only"?"#ff9500":"#34c759",text:e==="write_only"?"WRITE":"READ"}},getUrlTypeBadge(a){switch(a){case"absensi":return{color:"#007aff",text:"ABSENSI"};case"pantau":return{color:"#ff9500",text:"PANTAU"};case"statistik":return{color:"#34c759",text:"STATISTIK"};default:return{color:"#8e8e93",text:"UNKNOWN"}}},getUrlTypeFromLabel(a){return a.includes("Absensi")?"absensi":a.includes("Pantauan")?"pantau":a.includes("Statistik")?"statistik":"unknown"},validateDefaultKeys(){const a=this.validApiKeys;this.defaultReadKey&&(a.some(o=>o.id===this.defaultReadKey.id&&o.permission==="read_only")||(console.warn('🗑️ Default READ_ONLY key "'.concat(this.defaultReadKey.name,'" is no longer valid - clearing')),this.defaultReadKey=null,localStorage.removeItem("defaultReadKey"))),this.defaultWriteKey&&(a.some(o=>o.id===this.defaultWriteKey.id&&o.permission==="write_only")||(console.warn('🗑️ Default WRITE_ONLY key "'.concat(this.defaultWriteKey.name,'" is no longer valid - clearing')),this.defaultWriteKey=null,localStorage.removeItem("defaultWriteKey")))},async createApiKey(){if(!(!this.apiKeyForm.name||!this.auth.accessToken)){this.apiKeyForm.isCreating=!0;try{const a=["write_only","read_only"],e=[];for(const s of a){const i=s==="write_only"?"write":"read",r={name:"".concat(this.apiKeyForm.name,"-").concat(i),permission:s,expires_in_days:parseInt(this.apiKeyForm.expiresInDays,10)},c=await fetch("/api/auth/apikeys/create/",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(this.auth.accessToken)},body:JSON.stringify(r)});if(!c.ok){let v="Gagal membuat ".concat(s," key");try{v=(await c.json()).detail||v}catch(f){v="Server error (".concat(c.status,"): ").concat(c.statusText)}throw new Error(v)}const U=await c.json();e.push(U)}this.apiKeyForm.name="",await this.loadApiKeys();const o=e.find(s=>s.permission==="write_only"),n=e.find(s=>s.permission==="read_only");o&&this.setAsDefault(o),n&&this.setAsDefault(n),alert("Kedua API key (write & read) berhasil dibuat!")}catch(a){console.error("Create API keys error:",a),alert("Gagal membuat API keys: ".concat(a.message))}finally{this.apiKeyForm.isCreating=!1}}},async loadApiKeys(){if(this.auth.accessToken){this.apiKeys.isLoading=!0;try{const a=await fetch("/api/auth/apikeys/",{headers:{Authorization:"Bearer ".concat(this.auth.accessToken)}});if(!a.ok){let o="Gagal memuat API keys";try{o=(await a.json()).detail||o}catch(n){o="Server error (".concat(a.status,"): ").concat(a.statusText)}throw new Error(o)}const e=await a.json();this.apiKeys=e.map(o=>({...o,copied:!1})),this.validateDefaultKeys()}catch(a){console.error("Load API keys error:",a),alert("Gagal memuat API keys: ".concat(a.message))}finally{this.apiKeys.isLoading=!1}}},async revokeApiKey(a){if(confirm('Yakin ingin menghapus API key "'.concat(a.name,'"?')))try{const e=await fetch("/api/auth/apikeys/revoke/".concat(a.id,"/"),{method:"DELETE",headers:{Authorization:"Bearer ".concat(this.auth.accessToken),"Content-Type":"application/json"}});if(!e.ok){let o="Gagal menghapus API key";try{o=(await e.json()).detail||o}catch(n){o="Server error (".concat(e.status,"): ").concat(e.statusText)}throw new Error(o)}this.apiKeys=this.apiKeys.filter(o=>o.id!==a.id),this.defaultReadKey&&this.defaultReadKey.id===a.id&&(console.log("🗑️ Cleared default READ_ONLY key: ".concat(a.name)),this.defaultReadKey=null,localStorage.removeItem("defaultReadKey")),this.defaultWriteKey&&this.defaultWriteKey.id===a.id&&(console.log("🗑️ Cleared default WRITE_ONLY key: ".concat(a.name)),this.defaultWriteKey=null,localStorage.removeItem("defaultWriteKey")),this.urlGenerator.apiKey===a.key&&(this.urlGenerator.apiKey=""),alert("API key berhasil dihapus!")}catch(e){console.error("Revoke API key error:",e),alert("Gagal menghapus API key: ".concat(e.message))}},async copyApiKey(a){try{await navigator.clipboard.writeText(a.key),a.copied=!0,setTimeout(()=>{a.copied=!1},2e3)}catch(e){const o=this.$refs["apiKeyInput".concat(a.id)][0];o&&(o.select(),document.execCommand("copy"),a.copied=!0,setTimeout(()=>{a.copied=!1},2e3))}},buildUrl(a,e){const o=new URLSearchParams,n=e==="attendance"?this.defaultWriteKey:this.defaultReadKey;if(n&&n.key){o.set("key",n.key);const i=e==="attendance"?"WRITE_ONLY":"READ_ONLY",r=e==="attendance"?"writeKey":"readKey";this.loggedApiKeyUsage[r]||(console.log("🔑 Using default ".concat(i," key: ").concat(n.name)),this.loggedApiKeyUsage[r]=!0)}else{const i=e==="attendance"?"write_only":"read_only",r=e==="attendance"?"attendance (ngaji/asrama absen)":e==="monitoring"?"monitoring (pantau)":"statistics (statistik)",c=e==="attendance"?"writeKeyWarning":"readKeyWarning";this.loggedApiKeyUsage[c]||(console.warn("⚠️ No default ".concat(i," API key set for ").concat(r,". Please set a default key in the API Key section.")),this.loggedApiKeyUsage[c]=!0);const U=i.replace("_"," ").toUpperCase(),v=e==="attendance"?"writeKeyWarning":"readKeyWarning";if(!this.domWarningShown[v]&&!document.querySelector(".api-key-warning")){this.domWarningShown[v]=!0;const f=document.createElement("div");f.className="api-key-warning",f.style.cssText="\n                        position: fixed;\n                        top: 20px;\n                        right: 20px;\n                        background: #ff9500;\n                        color: white;\n                        padding: 12px 16px;\n                        border-radius: 8px;\n                        font-weight: 600;\n                        z-index: 1000;\n                        box-shadow: 0 4px 12px rgba(0,0,0,0.15);\n                        max-width: 300px;\n                        line-height: 1.4;\n                    ",f.innerHTML="⚠️ Set default ".concat(U," key first!<br><small>Needed for ").concat(r,"</small>"),document.body.appendChild(f),setTimeout(()=>{f.remove()},5e3)}}const s=this.urlGenerator.extraParams;return e==="attendance"?(this.addParam(o,"acara",s.acara),this.addParam(o,"lokasi",s.lokasi),this.addParam(o,"data",s.data),this.addParam(o,"ph",s.ph),this.urlGenerator.type==="asrama"&&this.addParam(o,"sesi",s.sesi)):e==="monitoring"?(this.addParam(o,"acara",s.acara),this.addParam(o,"lokasi",s.lokasi),this.addParam(o,"tanggal",s.tanggal)):e==="statistics"&&(this.addParam(o,"acara",s.acara),this.addParam(o,"lokasi",s.lokasi),this.addParam(o,"tanggal",s.tanggal),this.urlGenerator.type==="acara"&&this.addParam(o,"time",s.time)),"".concat(a,"?").concat(o.toString())},addParam(a,e,o){o&&o.trim()&&a.set(e,o.trim())},generateUrls(){console.log("🚀 Generate URLs button clicked!"),this.copyStatus=[!1,!1,!1],this.qrCodeUrl=null,this.shortUrls=[],this.showGeneratedUrls=!0;const a=this.generatedUrls.find(e=>e.label.includes("Absensi")&&(e.url.includes("/ngaji?")||e.url.includes("/asrama?")));console.log("📱 Generated ".concat(this.generatedUrls.length," URLs:"),this.generatedUrls.map(e=>e.label).join(", ")),a?(this.qrGenerator.url=a.url,this.generateQR(),this.shortenAllGeneratedUrls(),console.log("✅ URLs generated and sent to QR Code Generator & URL Shortener")):console.warn("⚠️ No absen URL found, but URLs are still generated")},async copyUrl(a,e){try{await navigator.clipboard.writeText(a),this.$set(this.copyStatus,e,!0),setTimeout(()=>this.$set(this.copyStatus,e,!1),2e3)}catch(o){const n=this.$refs["generatedUrlInput".concat(e)];n&&n[0]&&(n[0].select(),document.execCommand("copy"),this.$set(this.copyStatus,e,!0),setTimeout(()=>this.$set(this.copyStatus,e,!1),2e3))}},async copyAllUrls(){const a=this.generatedUrls.map(e=>"".concat(e.label,":\n").concat(e.url)).join("\n\n");try{await navigator.clipboard.writeText(a),this.copyStatus=[!0,!0,!0],setTimeout(()=>{this.copyStatus=[!1,!1,!1]},2e3)}catch(e){console.error("Failed to copy all URLs:",e),alert("Gagal menyalin URL. Silakan copy satu per satu.")}},generateQR(){if(console.log("🔍 generateQR called with URL:",this.qrGenerator.url),!this.qrGenerator.url){console.log("❌ No URL in qrGenerator");return}const a=this.qrGenerator.url,e=a.includes("/ngaji")||a.includes("/asrama"),o=a.includes("/pantau")||a.includes("/stat");if(console.log("🔍 URL validation - isAbsenUrl:",e,"isPantauOrStatUrl:",o),!e||o){console.log("❌ URL validation failed - not an absen URL"),alert("QR Code hanya dapat dibuat untuk URL absensi (ngaji atau asrama)");return}console.log("✅ URL validation passed - generating QR code");const n="https://api.qrserver.com/v1/create-qr-code/",s=new URLSearchParams({size:"".concat(this.qrGenerator.size,"x").concat(this.qrGenerator.size),data:this.qrGenerator.url,format:"png",margin:"10",color:"1d1d1f",bgcolor:"ffffff"});this.qrCodeUrl="".concat(n,"?").concat(s.toString())},downloadQR(){if(!this.qrCodeUrl)return;const a=document.createElement("a");a.href=this.qrCodeUrl,a.download="qr-code-".concat(Date.now(),".png"),document.body.appendChild(a),a.click(),document.body.removeChild(a)},async generatePDF(){if(!(!this.qrCodeUrl||!this.qrGenerator.customText.trim()))try{const{default:a}=await V(async()=>{const{default:S}=await import("./jspdf.es.min-CEEO7WE_.js").then(A=>A.j);return{default:S}},__vite__mapDeps([0,1,2,3])),e=new a({orientation:"portrait",unit:"mm",format:"a5"}),o=15,n=148,s=210,i=n-2*o;e.setFont("helvetica","bold"),e.setFontSize(25);const r="ABSENSI",c=e.getTextWidth(r),U=(n-c)/2;e.text(r,U,o+20),e.setFont("helvetica","normal"),e.setFontSize(18);const v=e.splitTextToSize(this.qrGenerator.customText.trim(),i);let f=o+35;v.forEach(S=>{const A=e.getTextWidth(S),x=(n-A)/2;e.text(S,x,f),f+=8}),f+=8;const K=new Image;K.crossOrigin="anonymous",await new Promise((S,A)=>{K.onload=()=>{try{const P=Math.min(i-20,100)+16+2,N=(n-P)/2;f+P>s-o&&(e.addPage(),f=o+20);const L=document.createElement("canvas"),l=L.getContext("2d"),y=400;L.width=y,L.height=y;const q=y*8/P,C=y*1/P,D=y-2*q-2*C,W=y*.05;l.fillStyle="#ffffff",l.fillRect(0,0,y,y),l.fillStyle="#f8f9fa",l.strokeStyle="#dee2e6",l.lineWidth=C;const _=(m,h,w,R,b)=>{l.beginPath(),l.moveTo(m+b,h),l.lineTo(m+w-b,h),l.quadraticCurveTo(m+w,h,m+w,h+b),l.lineTo(m+w,h+R-b),l.quadraticCurveTo(m+w,h+R,m+w-b,h+R),l.lineTo(m+b,h+R),l.quadraticCurveTo(m,h+R,m,h+R-b),l.lineTo(m,h+b),l.quadraticCurveTo(m,h,m+b,h),l.closePath()};_(0,0,y,y,W),l.fill(),l.stroke(),l.save();const F=q+C,j=q+C;_(F,j,D,D,W*.7),l.clip(),l.drawImage(K,F,j,D,D),l.restore();const z=L.toDataURL("image/png");e.addImage(z,"PNG",N,f,P,P),S()}catch(x){A(x)}},K.onerror=()=>A(new Error("Failed to load QR code image")),K.src=this.qrCodeUrl});const O="absensi-qr-".concat(Date.now(),".pdf");e.save(O)}catch(a){console.error("Error generating PDF:",a),alert("Gagal membuat PDF: ".concat(a.message))}},async shortenUrl(){const a=[],e=this.generatedUrls;if(e&&e.length>0){const o=e.find(i=>i.label.includes("Absensi"));o&&a.push(o);const n=e.find(i=>i.label.includes("Pantauan")),s=e.find(i=>i.label.includes("Statistik"));n&&a.push(n),s&&a.push(s)}if(a.length===0){alert("Silakan generate URL terlebih dahulu");return}this.isShortening=!0,this.shortenedUrls=[],this.shortUrlCopied=new Array(a.length).fill(!1);try{const o=this.defaultWriteKey||this.defaultReadKey;if(!o||!o.key){alert("API key diperlukan untuk memendekkan URL. Silakan set default API key terlebih dahulu.");return}for(const n of a){const s=new FormData;s.append("url",n.url);const i=await fetch("/api/url/",{method:"POST",headers:{Authorization:"ApiKey ".concat(o.key)},body:s});if(!i.ok){let c="Gagal memendekkan URL";try{c=(await i.json()).detail||c}catch(U){c="Server error (".concat(i.status,"): ").concat(i.statusText)}throw new Error(c)}const r=await i.json();this.shortenedUrls.push({label:n.label,url:"".concat(this.baseUrl,"/s/").concat(r.url_code),type:this.getUrlTypeFromLabel(n.label)})}}catch(o){console.error("Error shortening URL:",o),alert("Gagal memendekkan URL: ".concat(o.message))}finally{this.isShortening=!1}},async copyShortUrl(a){try{await navigator.clipboard.writeText(this.shortenedUrls[a].url),this.shortUrlCopied[a]=!0,setTimeout(()=>{this.shortUrlCopied[a]=!1},2e3)}catch(e){const o=this.$refs["shortenedUrlInput".concat(a)][0];o&&(o.select(),document.execCommand("copy"),this.shortUrlCopied[a]=!0,setTimeout(()=>{this.shortUrlCopied[a]=!1},2e3))}},async copyAllShortUrls(){try{const a=this.urlGenerator.extraParams.acara||"Event",e=this.shortenedUrls.find(i=>i.label.includes("Absensi")),o=this.shortenedUrls.find(i=>i.label.includes("Pantau")),n=this.shortenedUrls.find(i=>i.label.includes("Statistik")),s="----------\nLINK ABSENSI\n".concat(a,"\n\nABSENSI\n").concat(e?e.url:"URL tidak tersedia","\n\nPANTAU\n").concat(o?o.url:"URL tidak tersedia","\n\nSTATISTIK\n").concat(n?n.url:"URL tidak tersedia","\n----------");await navigator.clipboard.writeText(s),alert("Format link absensi berhasil disalin!")}catch(a){alert("Gagal menyalin URL. Silakan copy satu per satu.")}},generateQRForAbsen(){if(!this.generatedUrls||this.generatedUrls.length===0){alert("Silakan klik tombol 'Generate URLs' terlebih dahulu di bagian Generator Parameter URL.");return}const a=this.generatedUrls.find(e=>e.label.includes("Absensi")&&(e.url.includes("/ngaji?")||e.url.includes("/asrama?")));if(!a){alert("URL absensi tidak ditemukan. Pastikan Anda telah generate URLs dengan benar.");return}this.qrGenerator.url=a.url,this.generateQR()},shortenAllGeneratedUrls(){this.shortenUrl()},async fetchEndpointData(){if(this.dataManager.selectedEndpoint){this.dataManager.isLoading=!0,this.dataManager.error=null,this.dataManager.data=[];try{const a=this.urlGenerator.apiKey||this.getUrlParameter("key");if(!a)throw new Error("API key diperlukan untuk mengakses data. Silakan masukkan API key terlebih dahulu.");let e="/api/".concat(this.dataManager.selectedEndpoint,"/");this.dataManager.parameter&&(e+="".concat(encodeURIComponent(this.dataManager.parameter),"/"));const o=await fetch(e,{headers:{Authorization:"ApiKey ".concat(a),Accept:"application/json"}});if(!o.ok){let s="HTTP ".concat(o.status,": ").concat(o.statusText);try{s=(await o.json()).detail||s}catch(i){s="Server error (".concat(o.status,"): ").concat(o.statusText)}throw new Error(s)}const n=await o.json();this.dataManager.data=Array.isArray(n)?n:[n]}catch(a){console.error("Fetch endpoint data error:",a),this.dataManager.error=a.message}finally{this.dataManager.isLoading=!1}}},exportData(){if(!this.dataManager.data.length)return;const a=JSON.stringify(this.dataManager.data,null,2),e=new Blob([a],{type:"application/json"}),o=document.createElement("a");o.href=URL.createObjectURL(e),o.download="".concat(this.dataManager.selectedEndpoint.replace("/","-"),"-").concat(Date.now(),".json"),document.body.appendChild(o),o.click(),document.body.removeChild(o)},async testApiEndpoint(){if(this.apiTester.selectedEndpoint){this.apiTester.isLoading=!0,this.apiTester.error=null,this.apiTester.response=null;try{const a=this.urlGenerator.apiKey||this.getUrlParameter("key");if(!a)throw new Error("API key diperlukan untuk test API. Silakan masukkan API key terlebih dahulu.");let e="/api/".concat(this.apiTester.selectedEndpoint,"/");this.apiTester.method==="GET"&&this.apiTester.queryParams&&(e+="?".concat(this.apiTester.queryParams));const o=Date.now(),n={method:this.apiTester.method,headers:{Authorization:"ApiKey ".concat(a),Accept:"application/json"}};if(this.apiTester.method==="POST")if(this.apiTester.selectedEndpoint==="url"){const c=new FormData;try{const U=JSON.parse(this.apiTester.testDataJson||"{}");Object.keys(U).forEach(v=>{c.append(v,U[v])}),n.body=c}catch(U){throw new Error("Invalid JSON format in test data")}}else n.headers["Content-Type"]="application/json",n.body=this.apiTester.testDataJson||"{}";const s=await fetch(e,n),i=Date.now();let r;try{r=await s.json()}catch(c){r=await s.text()}this.apiTester.response={status:s.status,ok:s.ok,time:i-o,data:r}}catch(a){console.error("API test error:",a),this.apiTester.error=a.message}finally{this.apiTester.isLoading=!1}}},generateQRFromManualInput(){if(!this.additionalTools.manualUrl.trim())return;const a="https://api.qrserver.com/v1/create-qr-code/",e=new URLSearchParams({size:"".concat(this.additionalTools.qrSize,"x").concat(this.additionalTools.qrSize),data:this.additionalTools.manualUrl,format:"png",margin:"10",color:"1d1d1f",bgcolor:"ffffff"});this.additionalTools.qrCodeUrl="".concat(a,"?").concat(e.toString())},downloadAdditionalQR(){if(!this.additionalTools.qrCodeUrl)return;const a=document.createElement("a");a.href=this.additionalTools.qrCodeUrl,a.download="additional-qr-code-".concat(Date.now(),".png"),document.body.appendChild(a),a.click(),document.body.removeChild(a)},async generateAdditionalPDF(){if(!(!this.additionalTools.qrCodeUrl||!this.additionalTools.customText.trim()))try{const{default:a}=await V(async()=>{const{default:S}=await import("./jspdf.es.min-CEEO7WE_.js").then(A=>A.j);return{default:S}},__vite__mapDeps([0,1,2,3])),e=new a({orientation:"portrait",unit:"mm",format:"a5"}),o=15,n=148,s=210,i=n-2*o;e.setFont("helvetica","bold"),e.setFontSize(48);const r="ABSENSI",c=e.getTextWidth(r),U=(n-c)/2;e.text(r,U,o+25),e.setFont("helvetica","normal"),e.setFontSize(14);const v=e.splitTextToSize(this.additionalTools.customText.trim(),i);let f=o+35;v.forEach(S=>{const A=e.getTextWidth(S),x=(n-A)/2;e.text(S,x,f),f+=7}),f+=8;const K=new Image;K.crossOrigin="anonymous",await new Promise((S,A)=>{K.onload=()=>{try{const P=Math.min(i-20,100)+16+2,N=(n-P)/2;f+P>s-o&&(e.addPage(),f=o+20);const L=document.createElement("canvas"),l=L.getContext("2d"),y=400;L.width=y,L.height=y;const q=y*8/P,C=y*1/P,D=y-2*q-2*C,W=y*.05;l.fillStyle="#ffffff",l.fillRect(0,0,y,y),l.fillStyle="#f8f9fa",l.strokeStyle="#dee2e6",l.lineWidth=C;const _=(m,h,w,R,b)=>{l.beginPath(),l.moveTo(m+b,h),l.lineTo(m+w-b,h),l.quadraticCurveTo(m+w,h,m+w,h+b),l.lineTo(m+w,h+R-b),l.quadraticCurveTo(m+w,h+R,m+w-b,h+R),l.lineTo(m+b,h+R),l.quadraticCurveTo(m,h+R,m,h+R-b),l.lineTo(m,h+b),l.quadraticCurveTo(m,h,m+b,h),l.closePath()};_(0,0,y,y,W),l.fill(),l.stroke(),l.save();const F=q+C,j=q+C;_(F,j,D,D,W*.7),l.clip(),l.drawImage(K,F,j,D,D),l.restore();const z=L.toDataURL("image/png");e.addImage(z,"PNG",N,f,P,P),S()}catch(x){A(x)}},K.onerror=()=>A(new Error("Failed to load QR code image")),K.src=this.additionalTools.qrCodeUrl});const O="additional-absensi-qr-".concat(Date.now(),".pdf");e.save(O)}catch(a){console.error("Error generating PDF:",a),alert("Gagal membuat PDF: ".concat(a.message))}},async shortenManualUrl(){if(this.additionalTools.manualUrl.trim()){this.additionalTools.isProcessing=!0,this.additionalTools.shortenedManualUrl="";try{const a=this.defaultWriteKey||this.defaultReadKey;if(!a||!a.key){alert("API key diperlukan untuk memendekkan URL. Silakan set default API key terlebih dahulu.");return}const e=new FormData;e.append("url",this.additionalTools.manualUrl);const o=await fetch("/api/url/",{method:"POST",headers:{Authorization:"ApiKey ".concat(a.key)},body:e});if(!o.ok){let s="Gagal memendekkan URL";try{s=(await o.json()).detail||s}catch(i){s="Server error (".concat(o.status,"): ").concat(o.statusText)}throw new Error(s)}const n=await o.json();this.additionalTools.shortenedManualUrl="".concat(this.baseUrl,"/s/").concat(n.url_code)}catch(a){console.error("Error shortening manual URL:",a),alert("Gagal memendekkan URL: ".concat(a.message))}finally{this.additionalTools.isProcessing=!1}}},async copyAdditionalShortUrl(){try{await navigator.clipboard.writeText(this.additionalTools.shortenedManualUrl),this.additionalTools.copied=!0,setTimeout(()=>{this.additionalTools.copied=!1},2e3)}catch(a){const e=this.$refs.additionalShortenedUrlInput;e&&(e.select(),document.execCommand("copy"),this.additionalTools.copied=!0,setTimeout(()=>{this.additionalTools.copied=!1},2e3))}},getUrlParameter(a){return new URLSearchParams(window.location.search).get(a)||""},formatDate(a){if(!a)return"Tidak diketahui";try{return new Date(a).toLocaleDateString("id-ID",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}catch(e){return"Format tanggal tidak valid"}}},mounted(){document.title="Sistem Absensi Sabilillah";const a=this.getUrlParameter("key");a&&(this.urlGenerator.apiKey=a);try{const e=JSON.parse(localStorage.getItem(this.STORAGE_KEY)||"{}");e.accessToken&&(this.auth.username=e.username||"",this.auth.accessToken=e.accessToken,this.auth.isLoggedIn=!0,this.loadApiKeys().catch(()=>this.logout()))}catch(e){console.error("Error restoring auth state:",e),this.logout()}try{const e=localStorage.getItem("defaultReadKey"),o=localStorage.getItem("defaultWriteKey");e&&(this.defaultReadKey=JSON.parse(e)),o&&(this.defaultWriteKey=JSON.parse(o))}catch(e){console.error("Error restoring default API keys:",e),this.defaultReadKey=null,this.defaultWriteKey=null}}},te={class:"page-wrapper"},ae={class:"content-area"},se={class:"util-container"},re={class:"tool-section"},oe={key:0,class:"auth-section"},ie={class:"form-group"},ne={class:"form-group"},le=["disabled"],de={key:0,class:"error-message"},ue={key:1,class:"auth-section"},ce={class:"user-info"},pe={class:"welcome-text"},me={class:"api-key-creation"},he={class:"form-group"},ge={class:"form-group"},fe=["disabled"],ye={key:0,class:"api-keys-list"},ke={class:"api-key-info"},be={class:"api-key-name"},Ue={key:0,class:"api-key-default"},ve={class:"api-key-details"},Te={class:"api-key-expires"},Se={class:"api-key-value"},Ae=["value"],Pe=["onClick"],we={class:"api-key-actions"},Re=["onClick","disabled"],Ke=["onClick"],xe={key:1,class:"expired-keys-list"},Le={class:"expired-notice"},Ce={class:"api-key-info"},De={class:"api-key-name"},Ee={class:"api-key-details"},Ge={class:"api-key-expires expired"},Ie=["onClick"],qe=["disabled"],Me={class:"tool-section"},We={class:"form-group"},_e={class:"param-section"},Fe={class:"form-group"},je={class:"form-group"},Oe={class:"form-group"},Ne=["value"],ze={class:"form-group"},Qe={key:0,class:"form-group"},Be=["value"],Ve={class:"form-group"},Je={key:1,class:"form-group"},Ye={class:"form-group"},He={key:0,class:"generated-urls"},Xe={class:"url-label"},Ze={class:"url-display"},$e=["value"],et=["onClick"],tt={class:"url-actions"},at={class:"tool-section"},st={class:"quick-action-section"},rt=["disabled"],ot={class:"form-group"},it={class:"form-group"},nt={key:0,class:"qr-result"},lt={class:"qr-display"},dt=["src","alt"],ut={class:"qr-actions"},ct=["disabled"],pt={class:"tool-section"},mt={class:"quick-action-section"},ht=["disabled"],gt={key:0,class:"shortened-result"},ft={class:"url-label"},yt={class:"url-display"},kt=["value"],bt=["onClick"],Ut={class:"url-actions"},vt={class:"tool-section"},Tt={class:"form-group"},St={class:"additional-tool-section"},At={class:"form-group"},Pt=["disabled"],wt={key:0,class:"qr-result"},Rt={class:"qr-display"},Kt=["src","alt"],xt={class:"form-group"},Lt={class:"qr-actions"},Ct=["disabled"],Dt={class:"additional-tool-section"},Et=["disabled"],Gt={key:0,class:"shortened-result"},It={class:"url-display"},qt=["value"],Mt={class:"tool-section"},Wt={class:"form-group"},_t={key:0,class:"form-group"},Ft=["disabled"],jt={key:1,class:"error-message"},Ot={key:2,class:"data-result"},Nt={class:"section-subtitle"},zt={class:"data-summary"},Qt={class:"data-table-container"},Bt={class:"data-table"},Vt={key:0,class:"data-note"},Jt={class:"tool-section"},Yt={class:"form-group"},Ht={key:0,class:"form-group"},Xt={key:1,class:"form-group"},Zt={key:2,class:"form-group"},$t=["disabled"],ea={key:3,class:"error-message"},ta={key:4,class:"api-response"},aa={class:"response-info"},sa={class:"response-time"},ra={class:"response-body"};function oa(a,e,o,n,s,i){return u(),d("div",te,[t("div",ae,[t("div",se,[e[108]||(e[108]=t("div",{class:"util-header"},[t("h1",{class:"util-title"},"SISTEM ABSENSI SABILILLAH"),t("p",{class:"util-subtitle"},"Kumpulan Alat Absensi")],-1)),t("div",re,[e[55]||(e[55]=t("div",{class:"tool-header"},[t("h2",{class:"tool-title"},"🔐 Manajemen API Key"),t("p",{class:"tool-description"}," Login dan kelola API key untuk akses sistem ")],-1)),s.auth.isLoggedIn?(u(),d("div",ue,[t("div",ce,[t("p",pe,[e[46]||(e[46]=M(" Selamat datang, ",-1)),t("strong",null,p(s.auth.username),1),e[47]||(e[47]=M("! ",-1))]),t("button",{onClick:e[5]||(e[5]=(...r)=>i.logout&&i.logout(...r)),class:"action-button secondary small"}," Keluar ")]),t("div",me,[e[51]||(e[51]=t("h3",{class:"section-subtitle"}," Buat Pasangan API Key (Read & Write) ",-1)),e[52]||(e[52]=t("p",{class:"form-hint"}," Sistem akan membuat 2 API key sekaligus: Write-only (untuk absen ngaji/asrama) dan Read-only (untuk pantau/statistik) ",-1)),t("div",he,[e[48]||(e[48]=t("label",{class:"form-label"},"Nama API Key:",-1)),g(t("input",{"onUpdate:modelValue":e[6]||(e[6]=r=>s.apiKeyForm.name=r),type:"text",placeholder:"Contoh: Absensi-Guru-2025 (akan dibuat -write dan -read)",class:"form-input"},null,512),[[T,s.apiKeyForm.name]])]),t("div",ge,[e[50]||(e[50]=t("label",{class:"form-label"},"Berlaku Selama (hari):",-1)),g(t("select",{"onUpdate:modelValue":e[7]||(e[7]=r=>s.apiKeyForm.expiresInDays=r),class:"form-select"},e[49]||(e[49]=[Q('<option value="1" data-v-00c635c3>1 Hari</option><option value="3" data-v-00c635c3>3 Hari</option><option value="7" data-v-00c635c3>Seminggu</option><option value="30" data-v-00c635c3>Sebulan</option><option value="90" data-v-00c635c3>3 Bulan</option><option value="365" data-v-00c635c3>Setahun</option>',6)]),512),[[E,s.apiKeyForm.expiresInDays]])]),t("button",{onClick:e[8]||(e[8]=(...r)=>i.createApiKey&&i.createApiKey(...r)),class:"action-button",disabled:!s.apiKeyForm.name||s.apiKeyForm.isCreating},p(s.apiKeyForm.isCreating?"Membuat...":"Buat Read & Write Keys"),9,fe)]),i.validApiKeys.length>0?(u(),d("div",ye,[e[53]||(e[53]=t("h3",{class:"section-subtitle"},"API Keys Valid Anda",-1)),(u(!0),d(G,null,I(i.validApiKeys,r=>(u(),d("div",{class:"api-key-item",key:r.id},[t("div",ke,[t("div",be,[M(p(r.name)+" ",1),i.isDefault(r)?(u(),d("span",Ue,"DEFAULT")):k("",!0)]),t("div",ve,[t("span",{class:B(["api-key-permission",r.permission])},p(r.permission),3),t("span",Te,"Berlaku hingga: "+p(i.formatDate(r.expires_at)),1)]),t("div",Se,[t("input",{value:r.key,readonly:"",class:"form-input api-key-input",ref_for:!0,ref:"apiKeyInput"+r.id},null,8,Ae),t("button",{onClick:c=>i.copyApiKey(r),class:"copy-button small"},p(r.copied?"✓":"📋"),9,Pe)]),t("div",we,[t("button",{onClick:c=>i.setAsDefault(r),class:"action-button small",disabled:i.isDefault(r)},p(i.isDefault(r)?"Default":"Set as Default"),9,Re),t("button",{onClick:c=>i.revokeApiKey(r),class:"action-button danger small"}," Hapus ",8,Ke)])])]))),128))])):k("",!0),i.expiredApiKeys.length>0?(u(),d("div",xe,[e[54]||(e[54]=t("h3",{class:"section-subtitle"},"API Keys Kedaluwarsa",-1)),t("div",Le,[t("p",null,p(i.expiredApiKeys.length)+" API key telah kedaluwarsa dan perlu dihapus atau diperbaharui. ",1)]),(u(!0),d(G,null,I(i.expiredApiKeys,r=>(u(),d("div",{class:"api-key-item expired",key:r.id},[t("div",Ce,[t("div",De,p(r.name),1),t("div",Ee,[t("span",{class:B(["api-key-permission expired",r.permission])},p(r.permission),3),t("span",Ge,"Kedaluwarsa: "+p(i.formatDate(r.expires_at)),1)])]),t("button",{onClick:c=>i.revokeApiKey(r),class:"action-button danger small"}," Hapus ",8,Ie)]))),128))])):k("",!0),t("button",{onClick:e[9]||(e[9]=(...r)=>i.loadApiKeys&&i.loadApiKeys(...r)),class:"action-button secondary",disabled:s.apiKeys.isLoading},p(s.apiKeys.isLoading?"Memuat...":"Muat Ulang API Keys"),9,qe)])):(u(),d("div",oe,[t("div",ie,[e[44]||(e[44]=t("label",{class:"form-label"},"Username:",-1)),g(t("input",{"onUpdate:modelValue":e[0]||(e[0]=r=>s.auth.username=r),type:"text",placeholder:"Masukkan username",class:"form-input",onKeyup:e[1]||(e[1]=J((...r)=>i.login&&i.login(...r),["enter"]))},null,544),[[T,s.auth.username]])]),t("div",ne,[e[45]||(e[45]=t("label",{class:"form-label"},"Password:",-1)),g(t("input",{"onUpdate:modelValue":e[2]||(e[2]=r=>s.auth.password=r),type:"password",placeholder:"Masukkan password",class:"form-input",onKeyup:e[3]||(e[3]=J((...r)=>i.login&&i.login(...r),["enter"]))},null,544),[[T,s.auth.password]])]),t("button",{onClick:e[4]||(e[4]=(...r)=>i.login&&i.login(...r)),class:"action-button",disabled:!s.auth.username||!s.auth.password||s.auth.isLoggingIn},p(s.auth.isLoggingIn?"Masuk...":"Masuk"),9,le),s.auth.loginError?(u(),d("div",de,p(s.auth.loginError),1)):k("",!0)]))]),t("div",Me,[e[74]||(e[74]=t("div",{class:"tool-header"},[t("h2",{class:"tool-title"},"🔗 Generator Parameter URL"),t("p",{class:"tool-description"}," Buat URL dengan parameter untuk semua halaman sistem ")],-1)),t("div",We,[e[57]||(e[57]=t("label",{class:"form-label"},"Pilih Jenis:",-1)),g(t("select",{"onUpdate:modelValue":e[10]||(e[10]=r=>s.urlGenerator.type=r),class:"form-select"},e[56]||(e[56]=[t("option",{value:"acara"},"🎯 Acara (Event)",-1),t("option",{value:"asrama"},"🏠 Asrama (Dormitory)",-1)]),512),[[E,s.urlGenerator.type]]),e[58]||(e[58]=t("small",{class:"form-hint"}," Akan menghasilkan 3 URL sekaligus: Absensi, Pantauan, dan Statistik ",-1))]),t("div",_e,[e[71]||(e[71]=t("h3",{class:"section-subtitle"},"Parameter URL",-1)),t("div",Fe,[e[59]||(e[59]=t("label",{class:"form-label"},"Nama Acara:",-1)),g(t("input",{"onUpdate:modelValue":e[11]||(e[11]=r=>s.urlGenerator.extraParams.acara=r),type:"text",placeholder:"Contoh: Ngaji Muda-Mudi Daerah",class:"form-input",title:"Nama acara yang akan ditampilkan di halaman absensi"},null,512),[[T,s.urlGenerator.extraParams.acara]]),e[60]||(e[60]=t("small",{class:"form-hint"}," Gunakan dua spasi berturut-turut untuk membuat baris baru di tampilan ",-1))]),t("div",je,[e[61]||(e[61]=t("label",{class:"form-label"},"Lokasi:",-1)),g(t("input",{"onUpdate:modelValue":e[12]||(e[12]=r=>s.urlGenerator.extraParams.lokasi=r),type:"text",placeholder:"Contoh: Masjid Baitul Aziz",class:"form-input",title:"Lokasi pelaksanaan acara"},null,512),[[T,s.urlGenerator.extraParams.lokasi]])]),t("div",Oe,[e[63]||(e[63]=t("label",{class:"form-label"},"Data Parameter:",-1)),g(t("select",{"onUpdate:modelValue":e[13]||(e[13]=r=>s.urlGenerator.extraParams.data=r),class:"form-select",title:"Parameter data untuk mengambil daftar kelompok dari API"},[e[62]||(e[62]=t("option",{value:"",disabled:""},"Pilih data parameter",-1)),(u(!0),d(G,null,I(s.daerahOptions,r=>(u(),d("option",{key:r.value,value:r.value},p(r.text),9,Ne))),128))],512),[[E,s.urlGenerator.extraParams.data]]),e[64]||(e[64]=t("small",{class:"form-hint"}," Parameter ini digunakan untuk mengambil data kelompok dari endpoint /api/data/daerah/ ",-1))]),t("div",ze,[e[65]||(e[65]=t("label",{class:"form-label"},"Custom Placeholder (opsional):",-1)),g(t("input",{"onUpdate:modelValue":e[14]||(e[14]=r=>s.urlGenerator.extraParams.ph=r),type:"text",placeholder:"Contoh: KELOMPOK",class:"form-input",title:"Teks placeholder kustom untuk input kelompok"},null,512),[[T,s.urlGenerator.extraParams.ph]])]),i.showAsramaParams?(u(),d("div",Qe,[e[67]||(e[67]=t("label",{class:"form-label"},"Sesi (khusus asrama):",-1)),g(t("select",{"onUpdate:modelValue":e[15]||(e[15]=r=>s.urlGenerator.extraParams.sesi=r),class:"form-select",title:"Sesi untuk absensi asrama"},[e[66]||(e[66]=t("option",{value:"",disabled:""},"Pilih Sesi",-1)),(u(!0),d(G,null,I(s.sesiOptions,r=>(u(),d("option",{key:r.value,value:r.value},p(r.text),9,Be))),128))],512),[[E,s.urlGenerator.extraParams.sesi]])])):k("",!0),t("div",Ve,[e[68]||(e[68]=t("label",{class:"form-label"},"Filter Tanggal (opsional):",-1)),g(t("input",{"onUpdate:modelValue":e[16]||(e[16]=r=>s.urlGenerator.extraParams.tanggal=r),type:"date",class:"form-input",title:"Filter berdasarkan tanggal untuk pantauan dan statistik"},null,512),[[T,s.urlGenerator.extraParams.tanggal]])]),s.urlGenerator.type==="acara"?(u(),d("div",Je,[e[69]||(e[69]=t("label",{class:"form-label"},"Parameter Waktu Statistik (opsional):",-1)),g(t("input",{"onUpdate:modelValue":e[17]||(e[17]=r=>s.urlGenerator.extraParams.time=r),type:"time",placeholder:"Waktu Referensi",class:"form-input",title:"Waktu referensi untuk statistik ngaji"},null,512),[[T,s.urlGenerator.extraParams.time]]),e[70]||(e[70]=t("small",{class:"form-hint"}," Parameter waktu khusus untuk halaman statistik ngaji ",-1))])):k("",!0)]),t("div",Ye,[t("button",{onClick:e[18]||(e[18]=(...r)=>i.generateUrls&&i.generateUrls(...r)),class:"action-button primary"}," 🚀 Generate URLs "),e[72]||(e[72]=t("small",{class:"form-hint"}," Generate URLs dan kirim ke QR Code Generator & URL Shortener ",-1))]),s.showGeneratedUrls&&i.generatedUrls.length>0?(u(),d("div",He,[e[73]||(e[73]=t("label",{class:"form-label"},"URL yang Dihasilkan:",-1)),(u(!0),d(G,null,I(i.generatedUrls,(r,c)=>(u(),d("div",{key:c,class:"url-item"},[t("label",Xe,[M(p(r.label)+": ",1),t("span",{class:"key-type-badge",style:Y({backgroundColor:i.getKeyTypeBadge(r.label).color})},p(i.getKeyTypeBadge(r.label).text),5)]),t("div",Ze,[t("input",{value:r.url,readonly:"",class:"form-input url-input",ref_for:!0,ref:"generatedUrlInput".concat(c)},null,8,$e),t("button",{onClick:U=>i.copyUrl(r.url,c),class:"copy-button"},p(s.copyStatus[c]?"✓":"📋"),9,et)])]))),128)),t("div",tt,[t("button",{onClick:e[19]||(e[19]=(...r)=>i.copyAllUrls&&i.copyAllUrls(...r)),class:"action-button"}," 📋 Copy Semua URL ")])])):k("",!0)]),t("div",at,[e[80]||(e[80]=t("div",{class:"tool-header"},[t("h2",{class:"tool-title"},"📱 Generator QR Code"),t("p",{class:"tool-description"}," Buat QR code untuk URL absensi (ngaji atau asrama) ")],-1)),t("div",st,[t("button",{onClick:e[20]||(e[20]=(...r)=>i.generateQRForAbsen&&i.generateQRForAbsen(...r)),class:"quick-action-button",disabled:!i.hasAbsenUrl}," 🚀 Generate QR for Absen ",8,rt),e[75]||(e[75]=t("small",{class:"form-hint"}," Otomatis buat QR code untuk URL absensi yang telah dihasilkan ",-1))]),t("div",ot,[e[76]||(e[76]=t("label",{class:"form-label"},"Text Kustom untuk PDF:",-1)),g(t("input",{"onUpdate:modelValue":e[21]||(e[21]=r=>s.qrGenerator.customText=r),type:"text",placeholder:"Contoh: Kajian Pagi Sesi 1",class:"form-input"},null,512),[[T,s.qrGenerator.customText]]),e[77]||(e[77]=t("small",{class:"form-hint"},' Text ini akan muncul di bawah "ABSENSI" pada PDF ',-1))]),t("div",it,[e[79]||(e[79]=t("label",{class:"form-label"},"Ukuran QR Code:",-1)),g(t("select",{"onUpdate:modelValue":e[22]||(e[22]=r=>s.qrGenerator.size=r),class:"form-select"},e[78]||(e[78]=[t("option",{value:"200"},"Kecil (200x200)",-1),t("option",{value:"300"},"Sedang (300x300)",-1),t("option",{value:"400"},"Besar (400x400)",-1)]),512),[[E,s.qrGenerator.size]])]),s.qrCodeUrl?(u(),d("div",nt,[t("div",lt,[t("img",{src:s.qrCodeUrl,alt:"QR Code untuk "+s.qrGenerator.url,class:"qr-image"},null,8,dt)]),t("div",ut,[t("button",{onClick:e[23]||(e[23]=(...r)=>i.downloadQR&&i.downloadQR(...r)),class:"action-button secondary"}," 💾 Download QR Code "),t("button",{onClick:e[24]||(e[24]=(...r)=>i.generatePDF&&i.generatePDF(...r)),class:"action-button",disabled:!s.qrGenerator.customText.trim()}," 📄 Generate PDF (A5) ",8,ct)])])):k("",!0)]),t("div",pt,[e[83]||(e[83]=t("div",{class:"tool-header"},[t("h2",{class:"tool-title"},"✂️ Pemendek URL"),t("p",{class:"tool-description"}," Buat URL pendek untuk kemudahan berbagi ")],-1)),t("div",mt,[t("button",{onClick:e[25]||(e[25]=(...r)=>i.shortenAllGeneratedUrls&&i.shortenAllGeneratedUrls(...r)),class:"quick-action-button",disabled:!i.generatedUrls.length||s.isShortening},p(s.isShortening?"Memproses...":"🚀 Shorten All URLs"),9,ht),e[81]||(e[81]=t("small",{class:"form-hint"}," Otomatis pendekkan semua URL (Absen, Pantau, Statistik) yang telah dihasilkan ",-1))]),s.shortenedUrls.length>0?(u(),d("div",gt,[e[82]||(e[82]=t("label",{class:"form-label"},"URLs Pendek:",-1)),(u(!0),d(G,null,I(s.shortenedUrls,(r,c)=>(u(),d("div",{key:c,class:"url-item"},[t("label",ft,[M(p(r.label)+": ",1),t("span",{class:"key-type-badge",style:Y({backgroundColor:i.getUrlTypeBadge(r.type).color})},p(i.getUrlTypeBadge(r.type).text),5)]),t("div",yt,[t("input",{value:r.url,readonly:"",class:"form-input url-input",ref_for:!0,ref:"shortenedUrlInput".concat(c)},null,8,kt),t("button",{onClick:U=>i.copyShortUrl(c),class:"copy-button"},p(s.shortUrlCopied[c]?"✓":"📋"),9,bt)])]))),128)),t("div",Ut,[t("button",{onClick:e[26]||(e[26]=(...r)=>i.copyAllShortUrls&&i.copyAllShortUrls(...r)),class:"action-button"}," 📋 Copy Semua URL Pendek ")])])):k("",!0)]),t("div",vt,[e[91]||(e[91]=t("div",{class:"tool-header"},[t("h2",{class:"tool-title"},"🛠️ Additional Tools"),t("p",{class:"tool-description"}," Tools tambahan untuk QR code dan URL shortener dengan input manual ")],-1)),t("div",Tt,[e[84]||(e[84]=t("label",{class:"form-label"},"URL Manual:",-1)),g(t("input",{"onUpdate:modelValue":e[27]||(e[27]=r=>s.additionalTools.manualUrl=r),type:"text",placeholder:"Masukkan URL yang ingin diproses",class:"form-input"},null,512),[[T,s.additionalTools.manualUrl]])]),t("div",St,[e[88]||(e[88]=t("h3",{class:"section-subtitle"},"📱 QR Code Generator",-1)),t("div",At,[e[86]||(e[86]=t("label",{class:"form-label"},"Ukuran QR Code:",-1)),g(t("select",{"onUpdate:modelValue":e[28]||(e[28]=r=>s.additionalTools.qrSize=r),class:"form-select"},e[85]||(e[85]=[t("option",{value:"200"},"Kecil (200x200)",-1),t("option",{value:"300"},"Sedang (300x300)",-1),t("option",{value:"400"},"Besar (400x400)",-1)]),512),[[E,s.additionalTools.qrSize]])]),t("button",{onClick:e[29]||(e[29]=(...r)=>i.generateQRFromManualInput&&i.generateQRFromManualInput(...r)),class:"action-button",disabled:!s.additionalTools.manualUrl.trim()}," Buat QR Code ",8,Pt),s.additionalTools.qrCodeUrl?(u(),d("div",wt,[t("div",Rt,[t("img",{src:s.additionalTools.qrCodeUrl,alt:"QR Code untuk "+s.additionalTools.manualUrl,class:"qr-image"},null,8,Kt)]),t("div",xt,[e[87]||(e[87]=t("label",{class:"form-label"},"Teks Kustom untuk PDF:",-1)),g(t("input",{"onUpdate:modelValue":e[30]||(e[30]=r=>s.additionalTools.customText=r),type:"text",placeholder:"Masukkan teks yang akan ditampilkan di PDF",class:"form-input"},null,512),[[T,s.additionalTools.customText]])]),t("div",Lt,[t("button",{onClick:e[31]||(e[31]=(...r)=>i.downloadAdditionalQR&&i.downloadAdditionalQR(...r)),class:"action-button secondary"}," 💾 Download QR Code "),t("button",{onClick:e[32]||(e[32]=(...r)=>i.generateAdditionalPDF&&i.generateAdditionalPDF(...r)),class:"action-button",disabled:!s.additionalTools.customText.trim()}," 📄 Generate PDF ",8,Ct)])])):k("",!0)]),t("div",Dt,[e[90]||(e[90]=t("h3",{class:"section-subtitle"},"✂️ URL Shortener",-1)),t("button",{onClick:e[33]||(e[33]=(...r)=>i.shortenManualUrl&&i.shortenManualUrl(...r)),class:"action-button",disabled:!s.additionalTools.manualUrl.trim()||s.additionalTools.isProcessing},p(s.additionalTools.isProcessing?"Memproses...":"Pendekkan URL"),9,Et),s.additionalTools.shortenedManualUrl?(u(),d("div",Gt,[e[89]||(e[89]=t("label",{class:"form-label"},"URL Pendek:",-1)),t("div",It,[t("input",{value:s.additionalTools.shortenedManualUrl,readonly:"",class:"form-input url-input",ref:"additionalShortenedUrlInput"},null,8,qt),t("button",{onClick:e[34]||(e[34]=(...r)=>i.copyAdditionalShortUrl&&i.copyAdditionalShortUrl(...r)),class:"copy-button"},p(s.additionalTools.copied?"✓":"📋"),1)])])):k("",!0)])]),t("div",Mt,[e[97]||(e[97]=t("div",{class:"tool-header"},[t("h2",{class:"tool-title"},"📊 Manajemen Data"),t("p",{class:"tool-description"}," Kelola data sistem dan test API endpoints ")],-1)),t("div",Wt,[e[93]||(e[93]=t("label",{class:"form-label"},"Pilih Endpoint Data:",-1)),g(t("select",{"onUpdate:modelValue":e[35]||(e[35]=r=>s.dataManager.selectedEndpoint=r),class:"form-select"},e[92]||(e[92]=[Q('<option value="" data-v-00c635c3>Pilih endpoint...</option><optgroup label="Data Master" data-v-00c635c3><option value="data/daerah" data-v-00c635c3>Daerah (/api/data/daerah/)</option><option value="data/sesi" data-v-00c635c3>Sesi (/api/data/sesi/)</option><option value="data/materi" data-v-00c635c3>Materi (/api/data/materi/)</option><option value="data/hobi" data-v-00c635c3>Hobi (/api/data/hobi/)</option><option value="data/kelas-sekolah" data-v-00c635c3> Kelas Sekolah (/api/data/kelas-sekolah/) </option></optgroup><optgroup label="Wilayah" data-v-00c635c3><option value="wilayah" data-v-00c635c3>Wilayah (/api/wilayah/)</option></optgroup><optgroup label="Biodata" data-v-00c635c3><option value="biodata" data-v-00c635c3>Biodata (/api/biodata/)</option></optgroup>',4)]),512),[[E,s.dataManager.selectedEndpoint]])]),s.dataManager.selectedEndpoint?(u(),d("div",_t,[e[94]||(e[94]=t("label",{class:"form-label"},"Parameter Tambahan (opsional):",-1)),g(t("input",{"onUpdate:modelValue":e[36]||(e[36]=r=>s.dataManager.parameter=r),type:"text",placeholder:"Contoh: medan-timur-1 (untuk endpoint daerah)",class:"form-input"},null,512),[[T,s.dataManager.parameter]]),e[95]||(e[95]=t("small",{class:"form-hint"}," Beberapa endpoint memerlukan parameter tambahan seperti ID atau nama daerah ",-1))])):k("",!0),t("button",{onClick:e[37]||(e[37]=(...r)=>i.fetchEndpointData&&i.fetchEndpointData(...r)),class:"action-button",disabled:!s.dataManager.selectedEndpoint||s.dataManager.isLoading},p(s.dataManager.isLoading?"Memuat...":"Ambil Data"),9,Ft),s.dataManager.error?(u(),d("div",jt,p(s.dataManager.error),1)):k("",!0),s.dataManager.data&&s.dataManager.data.length>0?(u(),d("div",Ot,[t("h3",Nt," Data dari "+p(s.dataManager.selectedEndpoint),1),t("div",zt,[t("p",null,[e[96]||(e[96]=t("strong",null,"Total records:",-1)),M(" "+p(s.dataManager.data.length),1)]),t("button",{onClick:e[38]||(e[38]=(...r)=>i.exportData&&i.exportData(...r)),class:"action-button secondary small"}," 📥 Export JSON ")]),t("div",Qt,[t("table",Bt,[t("thead",null,[t("tr",null,[(u(!0),d(G,null,I(s.dataManager.data[0],(r,c)=>(u(),d("th",{key:c},p(c),1))),128))])]),t("tbody",null,[(u(!0),d(G,null,I(s.dataManager.data.slice(0,10),(r,c)=>(u(),d("tr",{key:c},[(u(!0),d(G,null,I(r,(U,v)=>(u(),d("td",{key:v},p(U),1))),128))]))),128))])]),s.dataManager.data.length>10?(u(),d("p",Vt," Menampilkan 10 dari "+p(s.dataManager.data.length)+" records. Export untuk melihat semua data. ",1)):k("",!0)])])):k("",!0)]),t("div",Jt,[e[107]||(e[107]=t("div",{class:"tool-header"},[t("h2",{class:"tool-title"},"🧪 Test API Endpoints"),t("p",{class:"tool-description"},"Test dan debug API endpoints sistem")],-1)),t("div",Yt,[e[99]||(e[99]=t("label",{class:"form-label"},"Pilih Endpoint untuk Test:",-1)),g(t("select",{"onUpdate:modelValue":e[39]||(e[39]=r=>s.apiTester.selectedEndpoint=r),class:"form-select"},e[98]||(e[98]=[Q('<option value="" data-v-00c635c3>Pilih endpoint...</option><optgroup label="Absensi" data-v-00c635c3><option value="absen-pengajian" data-v-00c635c3> Absensi Pengajian (/api/absen-pengajian/) </option><option value="absen-asramaan" data-v-00c635c3> Absensi Asramaan (/api/absen-asramaan/) </option></optgroup><optgroup label="URL Shortener" data-v-00c635c3><option value="url" data-v-00c635c3>URL Shortener (/api/url/)</option></optgroup>',3)]),512),[[E,s.apiTester.selectedEndpoint]])]),s.apiTester.selectedEndpoint?(u(),d("div",Ht,[e[101]||(e[101]=t("label",{class:"form-label"},"Method:",-1)),g(t("select",{"onUpdate:modelValue":e[40]||(e[40]=r=>s.apiTester.method=r),class:"form-select"},e[100]||(e[100]=[t("option",{value:"GET"},"GET",-1),t("option",{value:"POST"},"POST",-1)]),512),[[E,s.apiTester.method]])])):k("",!0),s.apiTester.selectedEndpoint&&s.apiTester.method==="POST"?(u(),d("div",Xt,[e[102]||(e[102]=t("label",{class:"form-label"},"Test Data (JSON):",-1)),g(t("textarea",{"onUpdate:modelValue":e[41]||(e[41]=r=>s.apiTester.testDataJson=r),class:"form-textarea",rows:"6",placeholder:'{"nama": "Test User", "acara": "Test Event"}'},null,512),[[T,s.apiTester.testDataJson]]),e[103]||(e[103]=t("small",{class:"form-hint"}," Masukkan data JSON untuk testing POST requests ",-1))])):k("",!0),s.apiTester.selectedEndpoint&&s.apiTester.method==="GET"?(u(),d("div",Zt,[e[104]||(e[104]=t("label",{class:"form-label"},"Query Parameters:",-1)),g(t("input",{"onUpdate:modelValue":e[42]||(e[42]=r=>s.apiTester.queryParams=r),type:"text",placeholder:"tanggal=2025-01-01&acara=Test",class:"form-input"},null,512),[[T,s.apiTester.queryParams]]),e[105]||(e[105]=t("small",{class:"form-hint"}," Format: param1=value1¶m2=value2 ",-1))])):k("",!0),t("button",{onClick:e[43]||(e[43]=(...r)=>i.testApiEndpoint&&i.testApiEndpoint(...r)),class:"action-button",disabled:!s.apiTester.selectedEndpoint||s.apiTester.isLoading},p(s.apiTester.isLoading?"Testing...":"Test Endpoint"),9,$t),s.apiTester.error?(u(),d("div",ea,p(s.apiTester.error),1)):k("",!0),s.apiTester.response?(u(),d("div",ta,[e[106]||(e[106]=t("h3",{class:"section-subtitle"},"Response",-1)),t("div",aa,[t("span",{class:B(["response-status",s.apiTester.response.ok?"success":"error"])}," Status: "+p(s.apiTester.response.status),3),t("span",sa," Time: "+p(s.apiTester.response.time)+"ms ",1)]),t("pre",ra,p(JSON.stringify(s.apiTester.response.data,null,2)),1)])):k("",!0)])])])])}const la=$(ee,[["render",oa],["__scopeId","data-v-00c635c3"]]);export{la as default};
