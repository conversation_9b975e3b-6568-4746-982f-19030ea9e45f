System.register(["./vendor-legacy-BmDVBcfe.js","./index-legacy-DRucldFb.js"],function(e,t){"use strict";var a,s,i,n,r,o,c,l,d,h;return{setters:[e=>{a=e.c,s=e.a,i=e.t,n=e.m,r=e.v,o=e.s,c=e.F,l=e.p,d=e.o},e=>{h=e._}],execute:function(){var t=document.createElement("style");t.textContent=".chart-container{width:100%;max-width:600px;margin:0 auto;position:relative;aspect-ratio:3/2}.chart-canvas{width:100%;height:100%}#pieChartSection{margin-top:20px;text-align:center}#tanggalFilter,select{padding:5px;margin-left:10px}#statisticsTable{margin:20px auto;border-collapse:collapse;width:50%}#statisticsTable th,#statisticsTable td{border:1px solid #ddd;padding:8px}#statisticsTable th{background-color:#f2f2f2;font-weight:700}#statisticsTable td{text-align:center}.mb-15{margin-bottom:15px;font-weight:700;text-align:center}.mb-10{margin-bottom:10px}\n/*$vite$:1*/",document.head.appendChild(t);let u=null,m=null;const g=async()=>(u||await new Promise(e=>{const t=document.createElement("script");t.src="https://cdn.jsdelivr.net/npm/chart.js@3.8.0",t.onload=()=>{u=window.Chart,u.register(u.Title,u.Tooltip,u.Legend,u.ArcElement,u.CategoryScale,u.LinearScale),e()},document.head.appendChild(t)}),u),p={name:"StatAsrama",data:()=>({selectedDate:"",selectedSesi:"",sesiOptions:[],referenceTime:"",statistics:[],chart:null,fetchedData:[],acara:"",isLoading:!1,sesiData:[]}),async created(){await this.fetchSesiData()},computed:{totalCount(){return this.statistics.reduce((e,t)=>e+Number(t.count),0)}},watch:{referenceTime:{handler(e){this.fetchedData?.length&&this.onReferenceTimeChange()},immediate:!1},acara:{handler(e){document.title=e?`Statistik Kehadiran Asrama - ${e}`:"Statistik Kehadiran Asrama"},immediate:!0},selectedDate:{handler(e){e&&(console.log("Date changed to:",e),m&&(m.destroy(),m=null),this.fetchedData=[],this.statistics=[],this.fetchData())},immediate:!1},selectedSesi:{async handler(e){if(m&&(m.destroy(),m=null),e&&this.sesiData){const t=this.sesiData.find(t=>t.sesi===e);t?.waktu&&(this.referenceTime=t.waktu)}this.fetchedData=[],this.statistics=[],this.fetchData()}}},methods:{async fetchSesiData(){try{const e=new URLSearchParams(window.location.search).get("sesi");if(!e)return void console.warn("Parameter sesi tidak ditemukan di URL");const t=encodeURIComponent(e),a=await fetch(`/api/data/sesi/${t}`);if(!a.ok)throw new Error("Network response was not ok");const s=await a.json();if(Array.isArray(s)){this.sesiData=s,this.sesiOptions=s.map(e=>e.sesi);const e=new URLSearchParams(window.location.search).get("time");if(e)this.referenceTime=e;else if(this.selectedSesi){const e=s.find(e=>e.sesi===this.selectedSesi);e?.waktu&&(this.referenceTime=e.waktu)}this.referenceTime||(this.referenceTime="08:55")}}catch(e){console.error("Error fetching sesi data:",e),console.warn("Failed to fetch sesi data:",e.message),this.referenceTime="08:55"}},async fetchData(){if(this.isLoading)console.log("Request in progress, skipping new request");else try{this.isLoading=!0,console.log("Fetching data for date:",this.selectedDate);const e=new URLSearchParams(window.location.search),t=e.get("key");this.acara=e.get("acara")||"";const a=e.get("lokasi")||"";let s=`/api/absen-asramaan/?tanggal=${this.selectedDate}&acara=${encodeURIComponent(this.acara)}&lokasi=${encodeURIComponent(a)}`;this.selectedSesi&&(s+=`&sesi=${encodeURIComponent(this.selectedSesi)}`),console.log("Requesting URL:",s);const i=await fetch(s,{headers:{Authorization:`ApiKey ${t}`,Accept:"application/json"}});if(!i.ok)throw new Error(`HTTP error! status: ${i.status}`);const n=await i.json();if(!Array.isArray(n))return console.error("Invalid data format received:",n),void this.handleNoData();this.fetchedData=n,this.processData(this.fetchedData)}catch(e){console.error("Error fetching data:",e),this.handleNoData(),console.warn(`Failed to fetch attendance data: ${e.message}`)}finally{this.isLoading=!1}},categorizeAttendance(e){if(!e)return"Unknown";const[t,a]=e.split(":").map(Number);if(Number.isNaN(t)||Number.isNaN(a))return"Unknown";const s=60*t+a,[i,n]=this.referenceTime.split(":").map(Number),r=60*i+n;return s<r?"In Time":s<=r+15?"On Time":"Terlambat"},initializeChart(e){e.classList.add("chart-canvas"),e.parentElement?.classList.add("chart-container")},async updateChart(e){try{const t=this.$refs.timePieChart;if(!t)return void console.error("Canvas element not found");t.classList.contains("chart-canvas")||this.initializeChart(t);const a=await g(),s=Object.keys(e),i=Object.values(e),n={"In Time":"#82EE84","On Time":"#36A2EB",Terlambat:"#FF2323",Unknown:"#CCCCCC"},r=s.map(e=>n[e]||"#CCCCCC");if(m)return m.data.labels=s,m.data.datasets[0].data=i,void m.update("none");m=new a(t,{type:"pie",data:{labels:s,datasets:[{data:i,backgroundColor:r}]},options:{responsive:!0,maintainAspectRatio:!0,animation:{duration:300},plugins:{legend:{position:"bottom",labels:{boxWidth:20,padding:10}},title:{display:!0,text:"Distribusi Kehadiran Berdasarkan Waktu",padding:10}}}})}catch(t){console.error("Error updating chart:",t),this.handleChartError(t)}},handleChartError(e){m&&(m.destroy(),m=null),console.error("Chart error:",e.message)},handleNoData(){m&&(m.destroy(),m=null),this.statistics=[],this.fetchedData=[],console.warn("No attendance data available for the selected date.")},processData(e){if(!Array.isArray(e)||!e.length)return console.error("Invalid or empty data format:",e),void this.handleNoData();try{const t=e.reduce((e,t)=>{if(!t?.jam_hadir)return e;const a=this.categorizeAttendance(t.jam_hadir);return a&&(e[a]=(e[a]||0)+1),e},{});if(0===Object.keys(t).length)return void this.handleNoData();requestAnimationFrame(()=>{this.updateChart(t),this.updateStatistics(t)})}catch(t){console.error("Error processing data:",t),this.handleNoData()}},updateStatistics(e){const t=Object.values(e).reduce((e,t)=>e+Number(t),0);this.statistics=Object.entries(e).map(([e,a])=>({category:e,count:a,percentage:(a/t*100).toFixed(1)}))},onReferenceTimeChange(){console.log("Reference time changed to:",this.referenceTime),this.fetchedData?.length&&(m&&(m.destroy(),m=null),this.processData(this.fetchedData))}},async mounted(){try{const e=new Date;this.selectedDate=`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")}`,await this.$nextTick();const t=this.$refs.dateInput;t&&t.addEventListener("change",e=>{this.selectedDate=e.target.value}),await this.fetchData()}catch(e){console.error("Error in mounted hook:",e),this.handleNoData()}}},f={id:"pieChartSection"},w={class:"mb-15"},D={class:"mb-10"},y={class:"mb-10"},b=["value"],C={ref:"timePieChart",width:"150",height:"150"},v={id:"statisticsTable"};e("default",h(p,[["render",function(e,t,h,u,m,g){return d(),a("div",f,[t[10]||(t[10]=s("h2",null,"Statistik Kehadiran Asrama",-1)),s("div",w,i(m.acara||"ASRAMA"),1),s("div",D,[t[4]||(t[4]=s("label",{for:"tanggalFilter"},"Tanggal:",-1)),n(s("input",{type:"date",ref:"dateInput","onUpdate:modelValue":t[0]||(t[0]=e=>m.selectedDate=e),onChange:t[1]||(t[1]=(...e)=>g.fetchData&&g.fetchData(...e))},null,544),[[r,m.selectedDate]])]),s("div",y,[t[6]||(t[6]=s("label",{for:"sesiFilter"},"Sesi:",-1)),n(s("select",{"onUpdate:modelValue":t[2]||(t[2]=e=>m.selectedSesi=e),onChange:t[3]||(t[3]=(...e)=>g.fetchData&&g.fetchData(...e))},[t[5]||(t[5]=s("option",{value:""},"Semua Sesi",-1)),(d(!0),a(c,null,l(m.sesiOptions,e=>(d(),a("option",{key:e,value:e},i(e),9,b))),128))],544),[[o,m.selectedSesi]])]),s("canvas",C,null,512),s("table",v,[t[9]||(t[9]=s("thead",null,[s("tr",null,[s("th",null,"Kehadiran"),s("th",null,"Jumlah"),s("th",null,"Persentase")])],-1)),s("tbody",null,[(d(!0),a(c,null,l(m.statistics,e=>(d(),a("tr",{key:e.category},[s("td",null,i(e.category),1),s("td",null,i(e.count),1),s("td",null,i(e.percentage)+"%",1)]))),128)),s("tr",null,[t[7]||(t[7]=s("td",null,[s("strong",null,"Total")],-1)),s("td",null,[s("strong",null,i(g.totalCount),1)]),t[8]||(t[8]=s("td",null,[s("strong",null,"100%")],-1))])])])])}]]))}}});
