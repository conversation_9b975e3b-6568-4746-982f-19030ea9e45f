System.register(["./vendor-legacy-BmDVBcfe.js","./index-legacy-DRucldFb.js"],function(e,o){"use strict";var t,a,n,i,r,s,l,d,c,p,m,h,g;return{setters:[e=>{t=e.c,a=e.a,n=e.k,i=e.n,r=e.t,s=e.l,l=e.m,d=e.v,c=e.F,p=e.p,m=e.q,h=e.o},e=>{g=e._}],execute:function(){var o=document.createElement("style");o.textContent='body{font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,sans-serif;margin:0;padding:0;background-color:#fff;color:#2c4a3e;display:flex;justify-content:center;align-items:center;height:100vh}.page-wrapper{display:flex;flex-direction:column;min-height:100vh;position:relative;padding-bottom:0;width:100%}.content-area{flex:1;position:relative}.footer-area{position:fixed;width:100%;margin-top:auto;padding-bottom:5px;bottom:0}.form-container{width:100%;max-width:360px;padding:20px;border-radius:20px;background-color:#f9f9f9;box-shadow:0 10px 25px rgba(44,74,62,.2),0 6px 12px rgba(44,74,62,.15);box-sizing:border-box;text-align:center;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transition:opacity .3s ease-in-out;z-index:100}.hidden{opacity:0;pointer-events:none}.form-title{font-size:20px;font-weight:700;color:#2e5a35;margin-bottom:10px}.form-date{font-size:16px;color:#2e5a35;margin-bottom:20px;opacity:.8}input,select{width:100%;padding:12px;margin-bottom:15px;border:1px solid #2e5a35;border-radius:20px;font-size:16px;transition:border-color .3s ease;box-sizing:border-box;-webkit-appearance:none;appearance:none;background-color:#fff;color:#2c4a3e}input::placeholder{color:#2e5a35;opacity:.9}input:focus,select:focus{border-color:#2e5a35;border-width:2px;outline:none}button{width:100%;padding:12px;border:none;border-radius:20px;background-color:#2e5a35;color:#fff;font-size:16px;cursor:pointer;transition:background-color .3s ease;box-sizing:border-box;margin-top:10px}button:hover{background-color:#3d7a47}.confirmation-message{font-size:20px;font-weight:700;color:#2e5a35}.suggestions{position:absolute;width:100%;max-width:360px;max-height:30vh;overflow-y:auto;background:#fff;border:1px solid #2e5a35;border-radius:10px;box-shadow:0 2px 4px rgba(0,0,0,.3);z-index:100000;top:100%;margin-top:5px;-webkit-overflow-scrolling:touch;overscroll-behavior:contain}.suggestion-item{padding:10px;cursor:pointer;text-align:left;color:#2c4a3e;touch-action:pan-y}.suggestion-item{padding:10px;cursor:pointer;text-align:left;color:#2c4a3e}.suggestion-item:hover{background-color:#e8f3ed}.event-title{margin:25px 0;font-weight:700;color:#2e5a35;font-size:20px;line-height:1.6}.event-title br{display:block;margin:8px 0;content:""}.suggestions-container{touch-action:pan-y;position:relative;z-index:200000!important}.suggestions-wrapper{position:absolute;top:100%;left:0;width:100%;z-index:200000!important}.suggestions{width:100%;max-width:360px;max-height:30vh;overflow-y:auto;background:#fff;border:1px solid #2e5a35;border-radius:10px;box-shadow:0 4px 12px rgba(0,0,0,.5);margin-top:5px;-webkit-overflow-scrolling:touch;overscroll-behavior:contain;z-index:200000!important}.warning-container{width:100%;max-width:360px;padding:15px;margin:0 auto;border-radius:10px;background-color:#c00;color:#fff;box-shadow:0 4px 8px rgba(44,74,62,.1);box-sizing:border-box;text-align:center;font-weight:700;animation:blinkColors 1s infinite;z-index:50;line-height:1.25}@keyframes blinkColors{0%,to{background-color:#c00;color:#fff}50%{background-color:#fff;color:#c00}}\n/*$vite$:1*/',document.head.appendChild(o),Array.prototype.flatMap||(Array.prototype.flatMap=function(e){return Array.prototype.concat.apply([],this.map(e))});const u={class:"page-wrapper"},k={class:"content-area"},f=["innerHTML"],b={class:"form-date"},w={class:"suggestions-container"},y=["placeholder"],v={class:"suggestions-wrapper"},x={key:0,class:"suggestions"},S=["onClick"],D={key:0,class:"form-container"};e("default",g({data:()=>({formData:{nama:"",ranah:"",detail_ranah:"",jam_hadir:"",tanggal:"",acara:"",lokasi:""},showSuccess:!1,kelompokInput:"",previousKelompokInput:"",showSuggestions:!1,kelompokOptions:{},isLoading:!0,loadError:null,dataLoaded:!1,placeholderText:"---",displayAcara:"",isMobileKeyboardVisible:!1,inputTimer:null,isComposing:!1}),computed:{flattenedKelompok(){return Object.entries(this.kelompokOptions).flatMap(([e,o])=>o.map(o=>({desa:e,kelompok:o})))},filteredKelompok(){const e=this.kelompokInput.toLowerCase();if(console.log(`Computing filteredKelompok with search term: "${e}"`),!e||e.length<1)return console.log("Search term too short or empty, returning empty results"),[];if(!this.dataLoaded)return console.log("Data not yet loaded, returning empty array"),[];console.log(`Finding matches in ${this.flattenedKelompok.length} total options`);const o=this.flattenedKelompok.filter(o=>o.kelompok.toLowerCase().includes(e)||o.desa.toLowerCase().includes(e));console.log(`Found ${o.length} initial matches for "${e}"`);const t=[],a=new Set;for(const n of o){const e=`${n.kelompok.toLowerCase()}-${n.desa.toLowerCase()}`;a.has(e)||(a.add(e),t.push(n))}return console.log(`Returned ${t.length} unique matches for suggestions`),t}},watch:{kelompokInput(e){console.log(`kelompokInput changed to "${e}" (length: ${e.length})`);const o=e.length>=1;console.log(`Setting showSuggestions to ${o} based on input length`),this.showSuggestions=o}},methods:{formatDate(e){const o=new Date(`${e}T00:00:00`),t=new Date(o.toLocaleString("en-US",{timeZone:"Asia/Jakarta"}));return`${["Minggu","Senin","Selasa","Rabu","Kamis","Jumat","Sabtu"][t.getDay()]}, ${t.getDate()} ${["Januari","Februari","Maret","April","Mei","Juni","Juli","Agustus","September","Oktober","November","Desember"][t.getMonth()]} ${t.getFullYear()}`},getUrlParameter:e=>new URLSearchParams(window.location.search).get(e)||"",handleKelompokInput(e){console.log(`handleKelompokInput called with current input: "${this.kelompokInput}"`),this.inputTimer&&clearTimeout(this.inputTimer),this.inputTimer=setTimeout(()=>{const e=this.$refs.kelompokInputEl;!this.kelompokInput&&e&&e.value&&(console.log(`Synchronizing input value: "${e.value}"`),this.kelompokInput=e.value),this.kelompokInput.length<this.previousKelompokInput.length&&(console.log("Deletion detected, clearing input"),this.kelompokInput=""),this.previousKelompokInput=this.kelompokInput;const o=this.kelompokInput.length>=1;console.log(`Setting showSuggestions to ${o} based on input length (${this.kelompokInput.length})`),this.showSuggestions=o,this.kelompokInput.includes(" (")&&this.kelompokInput.includes(")")||(console.log("New input detected, clearing previous selection"),this.formData.detail_ranah="",this.formData.ranah=""),this.dataLoaded||this.isLoading||(console.log("Data not loaded, retrying fetch..."),this.fetchKelompokData())},50)},handleKelompokFocus(){console.log(`handleKelompokFocus called with current input: "${this.kelompokInput}"`);const e=this.kelompokInput.length>=1;console.log(`Focus event: Setting showSuggestions to ${e} (input length: ${this.kelompokInput.length})`),this.showSuggestions=e},handleKelompokBlur(){console.log("handleKelompokBlur called, scheduling suggestion hide"),this._selectionInProgress||setTimeout(()=>{console.log("Blur timeout executed, hiding suggestions"),this.showSuggestions=!1},150)},selectKelompok(e){this._selectionInProgress=!0,console.log(`selectKelompok called with item: ${e.kelompok} (${e.desa})`),this.kelompokInput=`${e.kelompok} (${e.desa})`,this.formData.detail_ranah=e.kelompok,this.formData.ranah=e.desa,this.formData={...this.formData},console.log("Form data updated immediately:",{detail_ranah:this.formData.detail_ranah,ranah:this.formData.ranah}),setTimeout(()=>{this.showSuggestions=!1,this._selectionInProgress=!1,console.log("Verification after timeout:",{detail_ranah:this.formData.detail_ranah,ranah:this.formData.ranah})},200)},validateForm(){return this.formData.nama.trim()?this.formData.detail_ranah&&this.formData.ranah?this.flattenedKelompok.some(e=>e.kelompok.toLowerCase()===this.formData.detail_ranah.toLowerCase()&&e.desa.toLowerCase()===this.formData.ranah.toLowerCase())?(console.log("Form validation successful ✅"),!0):(console.error("Validation failed: Invalid kelompok selection",{input:{detail_ranah:this.formData.detail_ranah,ranah:this.formData.ranah},availableOptions:this.flattenedKelompok}),alert("Silahkan pilih kelompok sesuai dengan pilihan yang muncul saat Anda mengetik"),!1):(alert("Kelompok harus dipilih dari daftar yang tersedia"),!1):(alert("Nama harus diisi"),!1)},async submitForm(){if(console.group("Form Submission Process"),console.log("Starting form submission..."),console.log("Form data before validation:",JSON.stringify(this.formData)),!this.validateForm())return console.warn("Form validation failed, submission aborted"),void console.groupEnd();console.log("Generating detailed timestamp using Asia/Jakarta timezone");const e=new Date;console.log("Original Date object:",e),console.log("Browser timezone:",Intl.DateTimeFormat().resolvedOptions().timeZone);const o=new Date(e.toLocaleString("en-US",{timeZone:"Asia/Jakarta"}));console.log("Date conversion details:",{originalTimestamp:e.toISOString(),utcTime:e.toUTCString(),convertedTimestamp:o.toISOString(),convertedUTC:o.toUTCString(),rawLocaleString:e.toLocaleString("en-US",{timeZone:"Asia/Jakarta"})}),this.formData.jam_hadir=`${o.getHours().toString().padStart(2,"0")}:${o.getMinutes().toString().padStart(2,"0")}`,this.formData.tanggal=o.toLocaleDateString("en-CA",{timeZone:"Asia/Jakarta"}),console.log("Generated timestamp details:",{jam_hadir:this.formData.jam_hadir,tanggal:this.formData.tanggal,hours:o.getHours(),minutes:o.getMinutes(),day:o.getDate(),month:o.getMonth()+1,year:o.getFullYear(),rawDate:o.toString()});const t=this.getUrlParameter("key");if(console.log("API Key detection:",{present:!!t,keyLength:t?t.length:0,firstFourChars:t?`${t.substring(0,4)}...`:"none"}),!t)return console.error("API key not found in URL parameters"),console.log("Full URL:",window.location.href),console.log("URL params:",new URLSearchParams(window.location.search).toString()),alert("Kunci API tidak ditemukan. Silakan berikan kunci yang valid di URL"),void console.groupEnd();try{console.log("Preparing form payload for submission..."),console.log("Original form data:",JSON.stringify(this.formData,null,2)),console.log("Input field focus history:",this._debugInputHistory||"Not tracked"),console.log("Last focused element:",document.activeElement?document.activeElement.id||document.activeElement.tagName:"None");const e={...this.formData,nama:this.formData.nama.trim(),detail_ranah:this.formData.detail_ranah.trim(),ranah:this.formData.ranah.trim()};console.log("Processed payload:",JSON.stringify(e,null,2)),console.log("Browser details:",{userAgent:navigator.userAgent,language:navigator.language,cookiesEnabled:navigator.cookieEnabled,onLine:navigator.onLine,screenSize:`${window.screen.width}x${window.screen.height}`,viewport:`${window.innerWidth}x${window.innerHeight}`});const o=new FormData;for(const[t,a]of Object.entries(e))o.append(t,a);if(console.log("Sending form data to API endpoint..."),!navigator.onLine)throw new Error("Tidak ada koneksi internet. Mohon periksa koneksi Anda dan coba lagi.");const n=await fetch("/api/absen-pengajian/",{method:"POST",headers:{Authorization:`ApiKey ${t}`},body:o});let i;try{const e=await n.text();try{i=JSON.parse(e)}catch(a){throw console.error("Failed to parse response:",e),new Error("Respons server tidak valid. Silakan coba lagi nanti.")}}catch(a){return console.error("Failed to parse response as JSON:",a),void alert("Terjadi kesalahan saat memproses respons dari server. Silakan coba lagi nanti.")}if(422===n.status){const e=i.detail?Array.isArray(i.detail)?i.detail.join("\n"):i.detail:"Data yang dikirim tidak valid";return void alert(e)}if(n.ok&&i.id)this.showSuccess=!0;else{if(!i.detail)throw new Error("Server tidak merespon dengan benar. Silakan coba beberapa saat lagi.");{const e=Array.isArray(i.detail)?i.detail[0]:i.detail;"string"==typeof e&&e.includes("Duplicate entry detected")?alert("Data yang sama sudah dimasukkan, silakan cek kembali"):(console.error("Server error detail:",e),alert(`Error: ${e}`))}}}catch(n){console.error("Network or system error:",n),alert(`Gagal mengirim data: ${n.message||"Silakan periksa koneksi internet Anda dan coba lagi"}`)}finally{console.groupEnd()}},resetForm(){window.location.reload()},async fetchKelompokData(){console.log("Fetching kelompok data...");try{const e=this.getUrlParameter("data");if(!e)throw new Error("Parameter data tidak ditemukan di URL");const o=encodeURIComponent(e),t=await fetch(`/api/data/daerah/${o}/`);if(!t.ok)throw new Error("Network response was not ok");const a=await t.json(),n={};for(const i of a)n[i.ranah]||(n[i.ranah]=[]),n[i.ranah].push(i.detail_ranah);this.kelompokOptions=n,this.isLoading=!1,this.loadError=null,this.dataLoaded=!0}catch(e){console.error("Error fetching kelompok data:",e),this.loadError="Gagal memuat data kelompok. Silakan muat ulang halaman.",this.isLoading=!1,this.dataLoaded=!1,setTimeout(()=>{this.fetchKelompokData()},5e3)}},processDisplayText:e=>e.replace(/[ ]{2}|\n/g,"<br>"),handleKelompokKeyup(e){const o=this.$refs.kelompokInputEl;o&&this.kelompokInput!==o.value&&(console.log(`Keyup detected value mismatch. v-model: "${this.kelompokInput}", element: "${o.value}"`),this.kelompokInput=o.value,this.handleKelompokInput())},handleCompositionEnd(e){this.isComposing=!1,console.log(`Composition ended with text: "${e.data}"`),this.kelompokInput=e.target.value,this.handleKelompokInput()}},async mounted(){console.log("Component mounted"),window.addEventListener("error",e=>{e.filename?.includes("ngaji.brkh.work")&&(console.warn("Blocked script loading error:",e.filename),e.preventDefault())},!0);try{const e=function(e={}){const o={success:!0,purged:[]};try{if(e.localStorage&&(localStorage.clear(),o.purged.push("localStorage")),e.sessionStorage&&(sessionStorage.clear(),o.purged.push("sessionStorage")),e.specificKeys&&Array.isArray(e.specificKeys))for(const t of e.specificKeys)localStorage.removeItem(t),sessionStorage.removeItem(t),o.purged.push(`key:${t}`);return e.memoryCache&&o.purged.push("memoryCache"),o}catch(t){return{success:!1,error:t.message}}}({localStorage:!1,sessionStorage:!0,memoryCache:!0,specificKeys:["ngajiFormData","ngajiLastSubmission"]});console.log("Cache purge result:",e)}catch(i){console.warn("Cache purging failed:",i)}const e=window.visualViewport;e&&e.addEventListener("resize",()=>{const o=this.isMobileKeyboardVisible;this.isMobileKeyboardVisible=e.height<.8*window.innerHeight,console.log(`Viewport resize: keyboard visible changed from ${o} to ${this.isMobileKeyboardVisible}`),console.log(`Viewport height: ${e.height}, Window height: ${window.innerHeight}`),this.isMobileKeyboardVisible&&this.kelompokInput?(console.log("Keyboard detected AND input has value, showing suggestions"),this.showSuggestions=!0):!this.isMobileKeyboardVisible&&o&&console.log("Keyboard hidden, suggestion visibility unchanged")});try{await fetch(window.location.pathname,{cache:"reload",credentials:"same-origin"})}catch(r){console.warn("Cache clearing failed:",r)}const o=new Date,t=new Date(o.toLocaleString("en-US",{timeZone:"Asia/Jakarta"}));this.formData.tanggal=t.toLocaleDateString("en-CA",{timeZone:"Asia/Jakarta"});const a=this.getUrlParameter("acara");this.formData.acara=a,this.displayAcara=this.processDisplayText(a),this.formData.lokasi=this.getUrlParameter("lokasi");const n=this.getUrlParameter("ph");n&&(this.placeholderText=n),document.title=`Absensi Acara - ${a.replace(/\s{2,}/g," - ")}`,await this.fetchKelompokData(),document.addEventListener("touchend",e=>{e.target===this.$refs.kelompokInputEl&&setTimeout(()=>{const e=this.$refs.kelompokInputEl;e&&this.kelompokInput!==e.value&&(console.log(`Touch event detected value mismatch. v-model: "${this.kelompokInput}", element: "${e.value}"`),this.kelompokInput=e.value,this.handleKelompokInput())},100)})}},[["render",function(e,o,g,I,K,$){return h(),t("div",u,[a("div",k,[a("div",{class:i(["form-container",{hidden:K.showSuccess}])},[a("div",{class:"event-title",innerHTML:K.displayAcara||"UMUM"},null,8,f),o[15]||(o[15]=a("div",{class:"form-title"},"ABSENSI",-1)),a("div",b,r($.formatDate(K.formData.tanggal)),1),a("form",{onSubmit:o[12]||(o[12]=s((...e)=>$.submitForm&&$.submitForm(...e),["prevent"]))},[l(a("input",{type:"text","onUpdate:modelValue":o[0]||(o[0]=e=>K.formData.nama=e),placeholder:"NAMA",required:""},null,512),[[d,K.formData.nama]]),a("div",w,[l(a("input",{type:"text","onUpdate:modelValue":o[1]||(o[1]=e=>K.kelompokInput=e),onInput:o[2]||(o[2]=(...e)=>$.handleKelompokInput&&$.handleKelompokInput(...e)),onKeyup:o[3]||(o[3]=(...e)=>$.handleKelompokKeyup&&$.handleKelompokKeyup(...e)),onCompositionend:o[4]||(o[4]=(...e)=>$.handleCompositionEnd&&$.handleCompositionEnd(...e)),onFocus:o[5]||(o[5]=(...e)=>$.handleKelompokFocus&&$.handleKelompokFocus(...e)),onBlur:o[6]||(o[6]=(...e)=>$.handleKelompokBlur&&$.handleKelompokBlur(...e)),ref:"kelompokInputEl",placeholder:K.placeholderText,required:""},null,40,y),[[d,K.kelompokInput]]),a("div",v,[K.showSuggestions&&K.kelompokInput&&$.filteredKelompok.length?(h(),t("div",x,[(h(!0),t(c,null,p($.filteredKelompok,e=>(h(),t("div",{key:`${e.kelompok}-${e.desa}`,class:"suggestion-item",onClick:s(o=>$.selectKelompok(e),["stop"])},r(e.kelompok)+" ("+r(e.desa)+") ",9,S))),128))])):n("",!0)])]),l(a("input",{type:"hidden","onUpdate:modelValue":o[7]||(o[7]=e=>K.formData.detail_ranah=e)},null,512),[[d,K.formData.detail_ranah]]),l(a("input",{type:"hidden","onUpdate:modelValue":o[8]||(o[8]=e=>K.formData.ranah=e)},null,512),[[d,K.formData.ranah]]),l(a("input",{type:"hidden","onUpdate:modelValue":o[9]||(o[9]=e=>K.formData.jam_hadir=e)},null,512),[[d,K.formData.jam_hadir]]),l(a("input",{type:"hidden","onUpdate:modelValue":o[10]||(o[10]=e=>K.formData.lokasi=e)},null,512),[[d,K.formData.lokasi]]),l(a("input",{type:"hidden","onUpdate:modelValue":o[11]||(o[11]=e=>K.formData.tanggal=e)},null,512),[[d,K.formData.tanggal]]),o[14]||(o[14]=a("button",{type:"submit"},"Kirim Data",-1))],32)],2),K.showSuccess?(h(),t("div",D,[o[16]||(o[16]=a("div",{class:"confirmation-message"},[m(" DATA ABSEN ANDA"),a("br"),m("SUDAH KAMI TERIMA."),a("br"),a("br"),m("Alhamdulillah"),a("br"),m("Jazaa Kumullohu Khoiro. ")],-1)),a("button",{onClick:o[13]||(o[13]=(...e)=>$.resetForm&&$.resetForm(...e))},"Kembali")])):n("",!0)]),o[17]||(o[17]=a("div",{class:"footer-area"},[a("div",{class:"warning-container"},[m(" WARNING!!!"),a("br"),m("DILARANG mengoperasikan HP"),a("br"),m("selama acara berlangsung. ")])],-1))])}]]))}}});
