import{c as h,a as i,k,n as w,t as c,l as f,m as d,v as m,F as y,p as D,q as u,o as p}from"./vendor-Xnl27S3x.js";import{_ as S}from"./index-Br_OsRMh.js";Array.prototype.flatMap||(Array.prototype.flatMap=function(o){return Array.prototype.concat.apply([],this.map(o))});function v(o={}){const e={success:!0,purged:[]};try{if(o.localStorage&&(localStorage.clear(),e.purged.push("localStorage")),o.sessionStorage&&(sessionStorage.clear(),e.purged.push("sessionStorage")),o.specificKeys&&Array.isArray(o.specificKeys))for(const s of o.specificKeys)localStorage.removeItem(s),sessionStorage.removeItem(s),e.purged.push("key:".concat(s));return o.memoryCache&&e.purged.push("memoryCache"),e}catch(s){return{success:!1,error:s.message}}}const b={data(){return{formData:{nama:"",ranah:"",detail_ranah:"",jam_hadir:"",tanggal:"",acara:"",lokasi:""},showSuccess:!1,kelompokInput:"",previousKelompokInput:"",showSuggestions:!1,kelompokOptions:{},isLoading:!0,loadError:null,dataLoaded:!1,placeholderText:"---",displayAcara:"",isMobileKeyboardVisible:!1,inputTimer:null,isComposing:!1}},computed:{flattenedKelompok(){return Object.entries(this.kelompokOptions).flatMap(([o,e])=>e.map(s=>({desa:o,kelompok:s})))},filteredKelompok(){const o=this.kelompokInput.toLowerCase();if(console.log('Computing filteredKelompok with search term: "'.concat(o,'"')),!o||o.length<1)return console.log("Search term too short or empty, returning empty results"),[];if(!this.dataLoaded)return console.log("Data not yet loaded, returning empty array"),[];console.log("Finding matches in ".concat(this.flattenedKelompok.length," total options"));const e=this.flattenedKelompok.filter(n=>n.kelompok.toLowerCase().includes(o)||n.desa.toLowerCase().includes(o));console.log("Found ".concat(e.length,' initial matches for "').concat(o,'"'));const s=[],r=new Set;for(const n of e){const a="".concat(n.kelompok.toLowerCase(),"-").concat(n.desa.toLowerCase());r.has(a)||(r.add(a),s.push(n))}return console.log("Returned ".concat(s.length," unique matches for suggestions")),s}},watch:{kelompokInput(o){console.log('kelompokInput changed to "'.concat(o,'" (length: ').concat(o.length,")"));const e=o.length>=1;console.log("Setting showSuggestions to ".concat(e," based on input length")),this.showSuggestions=e}},methods:{formatDate(o){const e=["Minggu","Senin","Selasa","Rabu","Kamis","Jumat","Sabtu"],s=["Januari","Februari","Maret","April","Mei","Juni","Juli","Agustus","September","Oktober","November","Desember"],r=new Date("".concat(o,"T00:00:00")),n=new Date(r.toLocaleString("en-US",{timeZone:"Asia/Jakarta"}));return"".concat(e[n.getDay()],", ").concat(n.getDate()," ").concat(s[n.getMonth()]," ").concat(n.getFullYear())},getUrlParameter(o){return new URLSearchParams(window.location.search).get(o)||""},handleKelompokInput(o){console.log('handleKelompokInput called with current input: "'.concat(this.kelompokInput,'"')),this.inputTimer&&clearTimeout(this.inputTimer),this.inputTimer=setTimeout(()=>{const e=this.$refs.kelompokInputEl;!this.kelompokInput&&e&&e.value&&(console.log('Synchronizing input value: "'.concat(e.value,'"')),this.kelompokInput=e.value),this.kelompokInput.length<this.previousKelompokInput.length&&(console.log("Deletion detected, clearing input"),this.kelompokInput=""),this.previousKelompokInput=this.kelompokInput;const s=this.kelompokInput.length>=1;console.log("Setting showSuggestions to ".concat(s," based on input length (").concat(this.kelompokInput.length,")")),this.showSuggestions=s,(!this.kelompokInput.includes(" (")||!this.kelompokInput.includes(")"))&&(console.log("New input detected, clearing previous selection"),this.formData.detail_ranah="",this.formData.ranah=""),!this.dataLoaded&&!this.isLoading&&(console.log("Data not loaded, retrying fetch..."),this.fetchKelompokData())},50)},handleKelompokFocus(){console.log('handleKelompokFocus called with current input: "'.concat(this.kelompokInput,'"'));const o=this.kelompokInput.length>=1;console.log("Focus event: Setting showSuggestions to ".concat(o," (input length: ").concat(this.kelompokInput.length,")")),this.showSuggestions=o},handleKelompokBlur(){console.log("handleKelompokBlur called, scheduling suggestion hide"),this._selectionInProgress||setTimeout(()=>{console.log("Blur timeout executed, hiding suggestions"),this.showSuggestions=!1},150)},selectKelompok(o){this._selectionInProgress=!0,console.log("selectKelompok called with item: ".concat(o.kelompok," (").concat(o.desa,")")),this.kelompokInput="".concat(o.kelompok," (").concat(o.desa,")"),this.formData.detail_ranah=o.kelompok,this.formData.ranah=o.desa,this.formData={...this.formData},console.log("Form data updated immediately:",{detail_ranah:this.formData.detail_ranah,ranah:this.formData.ranah}),setTimeout(()=>{this.showSuggestions=!1,this._selectionInProgress=!1,console.log("Verification after timeout:",{detail_ranah:this.formData.detail_ranah,ranah:this.formData.ranah})},200)},validateForm(){return this.formData.nama.trim()?!this.formData.detail_ranah||!this.formData.ranah?(alert("Kelompok harus dipilih dari daftar yang tersedia"),!1):this.flattenedKelompok.some(e=>e.kelompok.toLowerCase()===this.formData.detail_ranah.toLowerCase()&&e.desa.toLowerCase()===this.formData.ranah.toLowerCase())?(console.log("Form validation successful ✅"),!0):(console.error("Validation failed: Invalid kelompok selection",{input:{detail_ranah:this.formData.detail_ranah,ranah:this.formData.ranah},availableOptions:this.flattenedKelompok}),alert("Silahkan pilih kelompok sesuai dengan pilihan yang muncul saat Anda mengetik"),!1):(alert("Nama harus diisi"),!1)},async submitForm(){if(console.group("Form Submission Process"),console.log("Starting form submission..."),console.log("Form data before validation:",JSON.stringify(this.formData)),!this.validateForm()){console.warn("Form validation failed, submission aborted"),console.groupEnd();return}console.log("Generating detailed timestamp using Asia/Jakarta timezone");const o=new Date;console.log("Original Date object:",o),console.log("Browser timezone:",Intl.DateTimeFormat().resolvedOptions().timeZone);const e=new Date(o.toLocaleString("en-US",{timeZone:"Asia/Jakarta"}));console.log("Date conversion details:",{originalTimestamp:o.toISOString(),utcTime:o.toUTCString(),convertedTimestamp:e.toISOString(),convertedUTC:e.toUTCString(),rawLocaleString:o.toLocaleString("en-US",{timeZone:"Asia/Jakarta"})}),this.formData.jam_hadir="".concat(e.getHours().toString().padStart(2,"0"),":").concat(e.getMinutes().toString().padStart(2,"0")),this.formData.tanggal=e.toLocaleDateString("en-CA",{timeZone:"Asia/Jakarta"}),console.log("Generated timestamp details:",{jam_hadir:this.formData.jam_hadir,tanggal:this.formData.tanggal,hours:e.getHours(),minutes:e.getMinutes(),day:e.getDate(),month:e.getMonth()+1,year:e.getFullYear(),rawDate:e.toString()});const s=this.getUrlParameter("key");if(console.log("API Key detection:",{present:!!s,keyLength:s?s.length:0,firstFourChars:s?"".concat(s.substring(0,4),"..."):"none"}),!s){console.error("API key not found in URL parameters"),console.log("Full URL:",window.location.href),console.log("URL params:",new URLSearchParams(window.location.search).toString()),alert("Kunci API tidak ditemukan. Silakan berikan kunci yang valid di URL"),console.groupEnd();return}try{console.log("Preparing form payload for submission..."),console.log("Original form data:",JSON.stringify(this.formData,null,2)),console.log("Input field focus history:",this._debugInputHistory||"Not tracked"),console.log("Last focused element:",document.activeElement?document.activeElement.id||document.activeElement.tagName:"None");const r={...this.formData,nama:this.formData.nama.trim(),detail_ranah:this.formData.detail_ranah.trim(),ranah:this.formData.ranah.trim()};console.log("Processed payload:",JSON.stringify(r,null,2)),console.log("Browser details:",{userAgent:navigator.userAgent,language:navigator.language,cookiesEnabled:navigator.cookieEnabled,onLine:navigator.onLine,screenSize:"".concat(window.screen.width,"x").concat(window.screen.height),viewport:"".concat(window.innerWidth,"x").concat(window.innerHeight)});const n=new FormData;for(const[l,g]of Object.entries(r))n.append(l,g);if(console.log("Sending form data to API endpoint..."),!navigator.onLine)throw new Error("Tidak ada koneksi internet. Mohon periksa koneksi Anda dan coba lagi.");const a=await fetch("/api/absen-pengajian/",{method:"POST",headers:{Authorization:"ApiKey ".concat(s)},body:n});let t;try{const l=await a.text();try{t=JSON.parse(l)}catch(g){throw console.error("Failed to parse response:",l),new Error("Respons server tidak valid. Silakan coba lagi nanti.")}}catch(l){console.error("Failed to parse response as JSON:",l),alert("Terjadi kesalahan saat memproses respons dari server. Silakan coba lagi nanti.");return}if(a.status===422){const l=t.detail?Array.isArray(t.detail)?t.detail.join("\n"):t.detail:"Data yang dikirim tidak valid";alert(l);return}if(a.ok&&t.id)this.showSuccess=!0;else if(t.detail){const l=Array.isArray(t.detail)?t.detail[0]:t.detail;typeof l=="string"&&l.includes("Duplicate entry detected")?alert("Data yang sama sudah dimasukkan, silakan cek kembali"):(console.error("Server error detail:",l),alert("Error: ".concat(l)))}else throw new Error("Server tidak merespon dengan benar. Silakan coba beberapa saat lagi.")}catch(r){console.error("Network or system error:",r),alert("Gagal mengirim data: ".concat(r.message||"Silakan periksa koneksi internet Anda dan coba lagi"))}finally{console.groupEnd()}},resetForm(){window.location.reload()},async fetchKelompokData(){console.log("Fetching kelompok data...");try{const o=this.getUrlParameter("data");if(!o)throw new Error("Parameter data tidak ditemukan di URL");const e=encodeURIComponent(o),s=await fetch("/api/data/daerah/".concat(e,"/"));if(!s.ok)throw new Error("Network response was not ok");const r=await s.json(),n={};for(const a of r)n[a.ranah]||(n[a.ranah]=[]),n[a.ranah].push(a.detail_ranah);this.kelompokOptions=n,this.isLoading=!1,this.loadError=null,this.dataLoaded=!0}catch(o){console.error("Error fetching kelompok data:",o),this.loadError="Gagal memuat data kelompok. Silakan muat ulang halaman.",this.isLoading=!1,this.dataLoaded=!1,setTimeout(()=>{this.fetchKelompokData()},5e3)}},processDisplayText(o){return o.replace(/[ ]{2}|\n/g,"<br>")},handleKelompokKeyup(o){const e=this.$refs.kelompokInputEl;e&&this.kelompokInput!==e.value&&(console.log('Keyup detected value mismatch. v-model: "'.concat(this.kelompokInput,'", element: "').concat(e.value,'"')),this.kelompokInput=e.value,this.handleKelompokInput())},handleCompositionEnd(o){this.isComposing=!1,console.log('Composition ended with text: "'.concat(o.data,'"')),this.kelompokInput=o.target.value,this.handleKelompokInput()}},async mounted(){console.log("Component mounted"),window.addEventListener("error",a=>{var t;(t=a.filename)!=null&&t.includes("ngaji.brkh.work")&&(console.warn("Blocked script loading error:",a.filename),a.preventDefault())},!0);try{const a=v({localStorage:!1,sessionStorage:!0,memoryCache:!0,specificKeys:["ngajiFormData","ngajiLastSubmission"]});console.log("Cache purge result:",a)}catch(a){console.warn("Cache purging failed:",a)}const o=window.visualViewport;o&&o.addEventListener("resize",()=>{const a=this.isMobileKeyboardVisible;this.isMobileKeyboardVisible=o.height<window.innerHeight*.8,console.log("Viewport resize: keyboard visible changed from ".concat(a," to ").concat(this.isMobileKeyboardVisible)),console.log("Viewport height: ".concat(o.height,", Window height: ").concat(window.innerHeight)),this.isMobileKeyboardVisible&&this.kelompokInput?(console.log("Keyboard detected AND input has value, showing suggestions"),this.showSuggestions=!0):!this.isMobileKeyboardVisible&&a&&console.log("Keyboard hidden, suggestion visibility unchanged")});try{await fetch(window.location.pathname,{cache:"reload",credentials:"same-origin"})}catch(a){console.warn("Cache clearing failed:",a)}const e=new Date,s=new Date(e.toLocaleString("en-US",{timeZone:"Asia/Jakarta"}));this.formData.tanggal=s.toLocaleDateString("en-CA",{timeZone:"Asia/Jakarta"});const r=this.getUrlParameter("acara");this.formData.acara=r,this.displayAcara=this.processDisplayText(r),this.formData.lokasi=this.getUrlParameter("lokasi");const n=this.getUrlParameter("ph");n&&(this.placeholderText=n),document.title="Absensi Acara - ".concat(r.replace(/\s{2,}/g," - ")),await this.fetchKelompokData(),document.addEventListener("touchend",a=>{a.target===this.$refs.kelompokInputEl&&setTimeout(()=>{const t=this.$refs.kelompokInputEl;t&&this.kelompokInput!==t.value&&(console.log('Touch event detected value mismatch. v-model: "'.concat(this.kelompokInput,'", element: "').concat(t.value,'"')),this.kelompokInput=t.value,this.handleKelompokInput())},100)})}},K={class:"page-wrapper"},I={class:"content-area"},A=["innerHTML"],L={class:"form-date"},C={class:"suggestions-container"},T=["placeholder"],E={class:"suggestions-wrapper"},F={key:0,class:"suggestions"},U=["onClick"],M={key:0,class:"form-container"};function N(o,e,s,r,n,a){return p(),h("div",K,[i("div",I,[i("div",{class:w(["form-container",{hidden:n.showSuccess}])},[i("div",{class:"event-title",innerHTML:n.displayAcara||"UMUM"},null,8,A),e[15]||(e[15]=i("div",{class:"form-title"},"ABSENSI",-1)),i("div",L,c(a.formatDate(n.formData.tanggal)),1),i("form",{onSubmit:e[12]||(e[12]=f((...t)=>a.submitForm&&a.submitForm(...t),["prevent"]))},[d(i("input",{type:"text","onUpdate:modelValue":e[0]||(e[0]=t=>n.formData.nama=t),placeholder:"NAMA",required:""},null,512),[[m,n.formData.nama]]),i("div",C,[d(i("input",{type:"text","onUpdate:modelValue":e[1]||(e[1]=t=>n.kelompokInput=t),onInput:e[2]||(e[2]=(...t)=>a.handleKelompokInput&&a.handleKelompokInput(...t)),onKeyup:e[3]||(e[3]=(...t)=>a.handleKelompokKeyup&&a.handleKelompokKeyup(...t)),onCompositionend:e[4]||(e[4]=(...t)=>a.handleCompositionEnd&&a.handleCompositionEnd(...t)),onFocus:e[5]||(e[5]=(...t)=>a.handleKelompokFocus&&a.handleKelompokFocus(...t)),onBlur:e[6]||(e[6]=(...t)=>a.handleKelompokBlur&&a.handleKelompokBlur(...t)),ref:"kelompokInputEl",placeholder:n.placeholderText,required:""},null,40,T),[[m,n.kelompokInput]]),i("div",E,[n.showSuggestions&&n.kelompokInput&&a.filteredKelompok.length?(p(),h("div",F,[(p(!0),h(y,null,D(a.filteredKelompok,t=>(p(),h("div",{key:"".concat(t.kelompok,"-").concat(t.desa),class:"suggestion-item",onClick:f(l=>a.selectKelompok(t),["stop"])},c(t.kelompok)+" ("+c(t.desa)+") ",9,U))),128))])):k("",!0)])]),d(i("input",{type:"hidden","onUpdate:modelValue":e[7]||(e[7]=t=>n.formData.detail_ranah=t)},null,512),[[m,n.formData.detail_ranah]]),d(i("input",{type:"hidden","onUpdate:modelValue":e[8]||(e[8]=t=>n.formData.ranah=t)},null,512),[[m,n.formData.ranah]]),d(i("input",{type:"hidden","onUpdate:modelValue":e[9]||(e[9]=t=>n.formData.jam_hadir=t)},null,512),[[m,n.formData.jam_hadir]]),d(i("input",{type:"hidden","onUpdate:modelValue":e[10]||(e[10]=t=>n.formData.lokasi=t)},null,512),[[m,n.formData.lokasi]]),d(i("input",{type:"hidden","onUpdate:modelValue":e[11]||(e[11]=t=>n.formData.tanggal=t)},null,512),[[m,n.formData.tanggal]]),e[14]||(e[14]=i("button",{type:"submit"},"Kirim Data",-1))],32)],2),n.showSuccess?(p(),h("div",M,[e[16]||(e[16]=i("div",{class:"confirmation-message"},[u(" DATA ABSEN ANDA"),i("br"),u("SUDAH KAMI TERIMA."),i("br"),i("br"),u("Alhamdulillah"),i("br"),u("Jazaa Kumullohu Khoiro. ")],-1)),i("button",{onClick:e[13]||(e[13]=(...t)=>a.resetForm&&a.resetForm(...t))},"Kembali")])):k("",!0)]),e[17]||(e[17]=i("div",{class:"footer-area"},[i("div",{class:"warning-container"},[u(" WARNING!!!"),i("br"),u("DILARANG mengoperasikan HP"),i("br"),u("selama acara berlangsung. ")])],-1))])}const _=S(b,[["render",N]]);export{_ as default};
