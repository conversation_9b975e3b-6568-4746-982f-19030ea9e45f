import{c as l,a as n,k,n as D,t as h,l as c,m,s as w,F as g,p as f,v as u,q as p,o as d}from"./vendor-Xnl27S3x.js";import{_ as y}from"./index-Br_OsRMh.js";const S={data(){return{formData:{nama:"",ranah:"",detail_ranah:"",jam_hadir:"",tanggal:"",acara:"",lokasi:"",sesi:""},sesiOptions:[],showSuccess:!1,kelompokInput:"",previousKelompokInput:"",showSuggestions:!1,kelompokOptions:{},isLoading:!0,loadError:null,dataLoaded:!1,placeholderText:"KELOMPOK (DESA) / DAPUKAN",displayAcara:"",isMobileKeyboardVisible:!1,inputTimer:null,isComposing:!1}},computed:{flattenedKelompok(){return Object.entries(this.kelompokOptions).flatMap(([a,e])=>e.map(i=>({desa:a,kelompok:i})))},filteredKelompok(){const a=this.kelompokInput.toLowerCase();if(!a||a.length<1)return[];if(!this.dataLoaded)return console.log("Data not yet loaded, returning empty array"),[];const e=this.flattenedKelompok.filter(t=>t.kelompok.toLowerCase().includes(a)||t.desa.toLowerCase().includes(a)),i=[],r=new Set;for(const t of e){const s="".concat(t.kelompok.toLowerCase(),"-").concat(t.desa.toLowerCase());r.has(s)||(r.add(s),i.push(t))}return i}},watch:{kelompokInput(a){console.log('kelompokInput changed to "'.concat(a,'" (length: ').concat(a.length,")"));const e=a.length>=1;console.log("Setting showSuggestions to ".concat(e," based on input length")),this.showSuggestions=e}},methods:{formatDate(a){const e=["Minggu","Senin","Selasa","Rabu","Kamis","Jumat","Sabtu"],i=["Januari","Februari","Maret","April","Mei","Juni","Juli","Agustus","September","Oktober","November","Desember"],r=new Date("".concat(a,"T00:00:00")),t=new Date(r.toLocaleString("en-US",{timeZone:"Asia/Jakarta"}));return"".concat(e[t.getDay()],", ").concat(t.getDate()," ").concat(i[t.getMonth()]," ").concat(t.getFullYear())},getUrlParameter(a){return new URLSearchParams(window.location.search).get(a)||""},handleKelompokInput(){console.log('handleKelompokInput called with current input: "'.concat(this.kelompokInput,'"')),this.inputTimer&&clearTimeout(this.inputTimer),this.inputTimer=setTimeout(()=>{const a=this.$refs.kelompokInputEl;!this.kelompokInput&&a&&a.value&&(console.log('Synchronizing input value: "'.concat(a.value,'"')),this.kelompokInput=a.value),this.kelompokInput.length<this.previousKelompokInput.length&&(console.log("Deletion detected, clearing input"),this.kelompokInput=""),this.previousKelompokInput=this.kelompokInput;const e=this.kelompokInput.length>=1;console.log("Setting showSuggestions to ".concat(e," based on input length (").concat(this.kelompokInput.length,")")),this.showSuggestions=e,(!this.kelompokInput.includes(" (")||!this.kelompokInput.includes(")"))&&(console.log("New input detected, clearing previous selection"),this.formData.detail_ranah="",this.formData.ranah=""),!this.dataLoaded&&!this.isLoading&&(console.log("Data not loaded, retrying fetch..."),this.fetchKelompokData())},50)},handleKelompokKeyup(a){const e=this.$refs.kelompokInputEl;e&&this.kelompokInput!==e.value&&(console.log('Keyup detected value mismatch. v-model: "'.concat(this.kelompokInput,'", element: "').concat(e.value,'"')),this.kelompokInput=e.value,this.handleKelompokInput())},handleCompositionEnd(a){this.isComposing=!1,console.log('Composition ended with text: "'.concat(a.data,'"')),this.kelompokInput=a.target.value,this.handleKelompokInput()},handleKelompokFocus(){console.log('handleKelompokFocus called with current input: "'.concat(this.kelompokInput,'"'));const a=this.kelompokInput.length>=1;console.log("Focus event: Setting showSuggestions to ".concat(a," (input length: ").concat(this.kelompokInput.length,")")),this.showSuggestions=a},handleKelompokBlur(){console.log("handleKelompokBlur called, scheduling suggestion hide"),this._selectionInProgress||setTimeout(()=>{console.log("Blur timeout executed, hiding suggestions"),this.showSuggestions=!1},150)},selectKelompok(a){this._selectionInProgress=!0,console.log("selectKelompok called with item: ".concat(a.kelompok," (").concat(a.desa,")")),this.kelompokInput="".concat(a.kelompok," (").concat(a.desa,")"),this.formData.detail_ranah=a.kelompok,this.formData.ranah=a.desa,this.formData={...this.formData},console.log("Form data updated immediately:",{detail_ranah:this.formData.detail_ranah,ranah:this.formData.ranah}),setTimeout(()=>{this.showSuggestions=!1,this._selectionInProgress=!1,console.log("Verification after timeout:",{detail_ranah:this.formData.detail_ranah,ranah:this.formData.ranah})},200)},validateForm(){return this.formData.nama.trim()?this.formData.sesi?!this.formData.detail_ranah||!this.formData.ranah?(alert("Kelompok harus dipilih dari daftar yang tersedia"),!1):this.flattenedKelompok.some(e=>e.kelompok.toLowerCase()===this.formData.detail_ranah.toLowerCase()&&e.desa.toLowerCase()===this.formData.ranah.toLowerCase())?!0:(alert("Silahkan pilih kelompok sesuai dengan pilihan yang muncul saat Anda mengetik"),!1):(alert("Sesi harus dipilih"),!1):(alert("Nama harus diisi"),!1)},async submitForm(){if(!this.validateForm())return;const a=new Date,e=new Date(a.toLocaleString("en-US",{timeZone:"Asia/Jakarta"}));this.formData.jam_hadir="".concat(e.getHours().toString().padStart(2,"0"),":").concat(e.getMinutes().toString().padStart(2,"0")),this.formData.tanggal=e.toLocaleDateString("en-CA",{timeZone:"Asia/Jakarta"});const i=this.getUrlParameter("key");if(!i){alert("Kunci API tidak ditemukan. Silakan berikan kunci yang valid di URL");return}try{const r={...this.formData,nama:this.formData.nama.trim(),detail_ranah:this.formData.detail_ranah.trim(),ranah:this.formData.ranah.trim()},t=await fetch("/api/absen-asramaan/",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"ApiKey ".concat(i)},body:JSON.stringify(r)});let s;try{const o=await t.text();s=JSON.parse(o)}catch(o){console.error("Failed to parse response as JSON:",o),alert("Terjadi kesalahan saat memproses respons dari server");return}if(t.status===422){const o=s.detail?Array.isArray(s.detail)?s.detail.join("\n"):s.detail:"Data yang dikirim tidak valid";alert(o);return}if(t.ok&&s.id)this.showSuccess=!0;else if(s.detail){const o=Array.isArray(s.detail)?s.detail[0]:s.detail;typeof o=="string"&&o.includes("Duplicate entry detected")?alert("Data yang sama sudah dimasukkan, silakan cek kembali"):(console.error("Server error detail:",o),alert("Error: ".concat(o)))}else throw new Error("Unexpected response format")}catch(r){console.error("Network or system error:",r),alert("Terjadi kesalahan saat mengirim data. Silakan coba lagi")}},resetForm(){window.location.reload()},async fetchKelompokData(){console.log("Fetching kelompok data...");try{const a=this.getUrlParameter("data");if(!a)throw new Error("Parameter data tidak ditemukan di URL");const e=encodeURIComponent(a),i=await fetch("/api/data/daerah/".concat(e,"/"));if(!i.ok)throw new Error("Network response was not ok");const r=await i.json(),t={};for(const s of r)t[s.ranah]||(t[s.ranah]=[]),t[s.ranah].push(s.detail_ranah);this.kelompokOptions=t,this.isLoading=!1,this.loadError=null,this.dataLoaded=!0}catch(a){console.error("Error fetching kelompok data:",a),this.loadError="Gagal memuat data kelompok. Silakan muat ulang halaman.",this.isLoading=!1,this.dataLoaded=!1,setTimeout(()=>{this.fetchKelompokData()},5e3)}},async fetchSesiData(){try{const a=this.getUrlParameter("sesi");if(!a){console.warn("Parameter sesi tidak ditemukan di URL");return}const e=encodeURIComponent(a),i=await fetch("/api/data/sesi/".concat(e));if(!i.ok)throw new Error("Network response was not ok");const r=await i.json();this.sesiOptions=Array.isArray(r)?r.map(t=>t.sesi):[]}catch(a){console.error("Error fetching sesi data:",a),alert("Gagal memuat data sesi. Silakan muat ulang halaman.")}},processDisplayText(a){return a.replace(/[ ]{2}|\n/g,"<br>")}},async mounted(){console.log("Component mounted");try{await fetch(window.location.pathname,{cache:"reload",credentials:"same-origin"})}catch(t){console.warn("Cache clearing failed:",t)}const a=new Date,e=new Date(a.toLocaleString("en-US",{timeZone:"Asia/Jakarta"}));this.formData.tanggal=e.toLocaleDateString("en-CA",{timeZone:"Asia/Jakarta"});const i=this.getUrlParameter("acara");this.formData.acara=i,this.displayAcara=this.processDisplayText(i),this.formData.lokasi=this.getUrlParameter("lokasi");const r=this.getUrlParameter("ph");r&&(this.placeholderText=r),document.title="Absensi Acara - ".concat(i.replace(/\s{2,}/g," - ")),await Promise.all([this.fetchKelompokData(),this.fetchSesiData()]),document.addEventListener("touchend",t=>{t.target===this.$refs.kelompokInputEl&&setTimeout(()=>{const s=this.$refs.kelompokInputEl;s&&this.kelompokInput!==s.value&&(console.log('Touch event detected value mismatch. v-model: "'.concat(this.kelompokInput,'", element: "').concat(s.value,'"')),this.kelompokInput=s.value,this.handleKelompokInput())},100)})}},I={class:"page-wrapper"},K={class:"content-area"},v=["innerHTML"],A={key:0,class:"event-date"},b=["disabled"],L=["value"],T={class:"suggestions-container"},E=["placeholder"],C={class:"suggestions-wrapper"},U={key:0,class:"suggestions"},P=["onClick"],F={key:0,class:"form-container"};function N(a,e,i,r,t,s){return d(),l("div",I,[n("div",K,[n("div",{class:D(["form-container",{hidden:t.showSuccess}])},[n("div",{class:"event-title",innerHTML:t.displayAcara||"ASRAMA"},null,8,v),e[17]||(e[17]=n("div",{class:"form-title"},"ABSENSI",-1)),t.formData.tanggal?(d(),l("div",A,h(s.formatDate(t.formData.tanggal)),1)):k("",!0),n("form",{onSubmit:e[13]||(e[13]=c((...o)=>s.submitForm&&s.submitForm(...o),["prevent"]))},[m(n("select",{"onUpdate:modelValue":e[0]||(e[0]=o=>t.formData.sesi=o),required:"",disabled:!t.sesiOptions.length},[e[15]||(e[15]=n("option",{value:"",disabled:"",selected:""},"SESI",-1)),(d(!0),l(g,null,f(t.sesiOptions,o=>(d(),l("option",{key:o,value:o},h(o),9,L))),128))],8,b),[[w,t.formData.sesi]]),m(n("input",{type:"text","onUpdate:modelValue":e[1]||(e[1]=o=>t.formData.nama=o),placeholder:"NAMA",required:""},null,512),[[u,t.formData.nama]]),n("div",T,[m(n("input",{type:"text","onUpdate:modelValue":e[2]||(e[2]=o=>t.kelompokInput=o),onInput:e[3]||(e[3]=(...o)=>s.handleKelompokInput&&s.handleKelompokInput(...o)),onKeyup:e[4]||(e[4]=(...o)=>s.handleKelompokKeyup&&s.handleKelompokKeyup(...o)),onCompositionend:e[5]||(e[5]=(...o)=>s.handleCompositionEnd&&s.handleCompositionEnd(...o)),onFocus:e[6]||(e[6]=(...o)=>s.handleKelompokFocus&&s.handleKelompokFocus(...o)),onBlur:e[7]||(e[7]=(...o)=>s.handleKelompokBlur&&s.handleKelompokBlur(...o)),ref:"kelompokInputEl",style:{"touch-action":"manipulation"},placeholder:t.placeholderText,required:""},null,40,E),[[u,t.kelompokInput]]),n("div",C,[t.showSuggestions&&t.kelompokInput&&s.filteredKelompok.length?(d(),l("div",U,[(d(!0),l(g,null,f(s.filteredKelompok,o=>(d(),l("div",{key:"".concat(o.kelompok,"-").concat(o.desa),class:"suggestion-item",onClick:c(M=>s.selectKelompok(o),["stop"])},h(o.kelompok)+" ("+h(o.desa)+") ",9,P))),128))])):k("",!0)])]),m(n("input",{type:"hidden","onUpdate:modelValue":e[8]||(e[8]=o=>t.formData.detail_ranah=o)},null,512),[[u,t.formData.detail_ranah]]),m(n("input",{type:"hidden","onUpdate:modelValue":e[9]||(e[9]=o=>t.formData.ranah=o)},null,512),[[u,t.formData.ranah]]),m(n("input",{type:"hidden","onUpdate:modelValue":e[10]||(e[10]=o=>t.formData.jam_hadir=o)},null,512),[[u,t.formData.jam_hadir]]),m(n("input",{type:"hidden","onUpdate:modelValue":e[11]||(e[11]=o=>t.formData.lokasi=o)},null,512),[[u,t.formData.lokasi]]),m(n("input",{type:"hidden","onUpdate:modelValue":e[12]||(e[12]=o=>t.formData.tanggal=o)},null,512),[[u,t.formData.tanggal]]),e[16]||(e[16]=n("button",{type:"submit"},"Kirim Data",-1))],32)],2),t.showSuccess?(d(),l("div",F,[e[18]||(e[18]=n("div",{class:"confirmation-message"},[p(" DATA ABSEN ANDA"),n("br"),p("SUDAH KAMI TERIMA."),n("br"),n("br"),p("Alhamdulillah"),n("br"),p("Jazaa Kumullohu Khoiro. ")],-1)),n("button",{onClick:e[14]||(e[14]=(...o)=>s.resetForm&&s.resetForm(...o))},"Kembali")])):k("",!0)]),e[19]||(e[19]=n("div",{class:"footer-area"},[n("div",{class:"warning-container"},[p(" WARNING!!!"),n("br"),p("DILARANG mengoperasikan HP"),n("br"),p("selama acara berlangsung. ")])],-1))])}const _=y(S,[["render",N]]);export{_ as default};
