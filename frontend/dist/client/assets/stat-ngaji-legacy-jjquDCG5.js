System.register(["./vendor-legacy-BmDVBcfe.js","./index-legacy-DRucldFb.js"],function(t,e){"use strict";var a,n,i,r,s,o,c,l,d;return{setters:[t=>{a=t.c,n=t.a,i=t.t,r=t.m,s=t.v,o=t.F,c=t.p,l=t.o},t=>{d=t._}],execute:function(){var e=document.createElement("style");e.textContent=".chart-container{width:100%;max-width:600px;margin:0 auto;position:relative;aspect-ratio:3/2}.chart-canvas{width:100%;height:100%}#pieChartSection{margin-top:20px;text-align:center}#tanggalFilter{padding:5px;margin-left:10px}#statisticsTable{margin:20px auto;border-collapse:collapse;width:50%}#statisticsTable th,#statisticsTable td{border:1px solid #ddd;padding:8px}#statisticsTable th{background-color:#f2f2f2;font-weight:700}#statisticsTable td{text-align:center}.mb-15{margin-bottom:15px;font-weight:700;text-align:center}.mb-10{margin-bottom:10px}\n/*$vite$:1*/",document.head.appendChild(e);let h=null,u=null;const g=async()=>(h||await new Promise(t=>{const e=document.createElement("script");e.src="https://cdn.jsdelivr.net/npm/chart.js@3.8.0",e.onload=()=>{h=window.Chart,h.register(h.Title,h.Tooltip,h.Legend,h.ArcElement,h.CategoryScale,h.LinearScale),t()},document.head.appendChild(e)}),h),m={data:()=>({selectedDate:"",referenceTime:new URLSearchParams(window.location.search).get("time")||"08:55",statistics:[],chart:null,fetchedData:[],acara:"",isLoading:!1}),computed:{totalCount(){return this.statistics.reduce((t,e)=>t+Number(e.count),0)}},watch:{referenceTime:{handler(t){this.fetchedData?.length&&requestAnimationFrame(()=>{this.processData(this.fetchedData)})},immediate:!1},acara:{handler(t){document.title=t?`Statistik Kehadiran Acara - ${t}`:"Statistik Kehadiran Acara"},immediate:!0},selectedDate:{handler(t){t&&(console.log("Date changed to:",t),u&&(u.destroy(),u=null),this.fetchedData=[],this.statistics=[],this.fetchData())},immediate:!1}},methods:{async fetchData(){if(this.isLoading)console.log("Request in progress, skipping new request");else try{this.isLoading=!0,console.log("Fetching data for date:",this.selectedDate);const t=new URLSearchParams(window.location.search),e=t.get("key");this.acara=t.get("acara")||"";const a=t.get("lokasi")||"",n=`/api/absen-pengajian/?tanggal=${this.selectedDate}&acara=${encodeURIComponent(this.acara)}&lokasi=${encodeURIComponent(a)}`;console.log("Requesting URL:",n);const i=await fetch(n,{headers:{Authorization:`ApiKey ${e}`,Accept:"application/json"}});if(!i.ok)throw new Error(`HTTP error! status: ${i.status}`);const r=await i.json();if(!Array.isArray(r))return console.error("Invalid data format received:",r),void this.handleNoData();this.fetchedData=r,this.processData(this.fetchedData)}catch(t){console.error("Error fetching data:",t),this.handleNoData(),console.warn(`Failed to fetch attendance data: ${t.message}`)}finally{this.isLoading=!1}},onReferenceTimeChange(){console.log("Reference time changed to:",this.referenceTime),this.fetchedData?.length&&(u&&(u.destroy(),u=null),this.processData(this.fetchedData))},categorizeAttendance(t){if(!t)return"Unknown";const[e,a]=t.split(":").map(Number);if(Number.isNaN(e)||Number.isNaN(a))return"Unknown";const n=60*e+a,[i,r]=this.referenceTime.split(":").map(Number),s=60*i+r;return n<s?"In Time":n<=s+15?"On Time":"Terlambat"},initializeChart(t){t.classList.add("chart-canvas"),t.parentElement?.classList.add("chart-container")},async updateChart(t){try{const e=this.$refs.timePieChart;if(!e)return void console.error("Canvas element not found");e.classList.contains("chart-canvas")||this.initializeChart(e);const a=await g(),n=Object.keys(t),i=Object.values(t),r={"In Time":"#82EE84","On Time":"#36A2EB",Terlambat:"#FF2323",Unknown:"#CCCCCC"},s=n.map(t=>r[t]||"#CCCCCC");if(u)return u.data.labels=n,u.data.datasets[0].data=i,void u.update("none");u=new a(e,{type:"pie",data:{labels:n,datasets:[{data:i,backgroundColor:s}]},options:{responsive:!0,maintainAspectRatio:!0,animation:{duration:300},plugins:{legend:{position:"bottom",labels:{boxWidth:20,padding:10}},title:{display:!0,text:"Distribusi Kehadiran Berdasarkan Waktu",padding:10}}}})}catch(e){console.error("Error updating chart:",e),this.handleChartError(e)}},handleChartError(t){u&&(u.destroy(),u=null),console.error("Chart error:",t.message)},handleNoData(){u&&(u.destroy(),u=null),this.statistics=[],this.fetchedData=[],console.warn("No attendance data available for the selected date.")},processData(t){if(!Array.isArray(t)||!t.length)return console.error("Invalid or empty data format:",t),void this.handleNoData();try{const e=t.reduce((t,e)=>{if(!e?.jam_hadir)return t;const a=this.categorizeAttendance(e.jam_hadir);return a&&(t[a]=(t[a]||0)+1),t},{});if(0===Object.keys(e).length)return void this.handleNoData();requestAnimationFrame(()=>{this.updateChart(e),this.updateStatistics(e)})}catch(e){console.error("Error processing data:",e),this.handleNoData()}},updateStatistics(t){const e=Object.values(t).reduce((t,e)=>t+Number(e),0);this.statistics=Object.entries(t).map(([t,a])=>({category:t,count:a,percentage:(a/e*100).toFixed(1)}))},getTodayDate(){const t=new Date;return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")}`}},async mounted(){try{const t=new URLSearchParams(window.location.search).get("tanggal");if(t)if(/^\d{4}-\d{2}-\d{2}$/.test(t)){const e=new Date(t);isNaN(e.getTime())||t!==e.toISOString().split("T")[0]?(console.warn(`Invalid date parameter: ${t}, using today's date`),this.selectedDate=this.getTodayDate()):this.selectedDate=t}else console.warn(`Invalid date format: ${t}, expected yyyy-mm-dd, using today's date`),this.selectedDate=this.getTodayDate();else this.selectedDate=this.getTodayDate();await this.$nextTick();const e=this.$refs.dateInput;e&&e.addEventListener("change",t=>{this.selectedDate=t.target.value}),await this.fetchData()}catch(t){console.error("Error in mounted hook:",t),this.handleNoData()}}},p={id:"pieChartSection"},f={class:"mb-15"},y={class:"mb-10"},D={class:"mb-10"},b={ref:"timePieChart",width:"150",height:"150"},w={id:"statisticsTable"};t("default",d(m,[["render",function(t,e,d,h,u,g){return l(),a("div",p,[e[9]||(e[9]=n("h2",null,"Statistik",-1)),n("div",f,i(u.acara||"UMUM"),1),n("div",y,[e[4]||(e[4]=n("label",{for:"tanggalFilter"},"Tanggal:",-1)),r(n("input",{type:"date",ref:"dateInput","onUpdate:modelValue":e[0]||(e[0]=t=>u.selectedDate=t),onChange:e[1]||(e[1]=(...t)=>g.fetchData&&g.fetchData(...t))},null,544),[[s,u.selectedDate]])]),n("div",D,[e[5]||(e[5]=n("label",{for:"referenceTime"},"Waktu Referensi:",-1)),r(n("input",{type:"time",id:"referenceTime","onUpdate:modelValue":e[2]||(e[2]=t=>u.referenceTime=t),onChange:e[3]||(e[3]=(...t)=>g.onReferenceTimeChange&&g.onReferenceTimeChange(...t))},null,544),[[s,u.referenceTime]])]),n("canvas",b,null,512),n("table",w,[e[8]||(e[8]=n("thead",null,[n("tr",null,[n("th",null,"Kehadiran"),n("th",null,"Jumlah"),n("th",null,"Persentase")])],-1)),n("tbody",null,[(l(!0),a(o,null,c(u.statistics,t=>(l(),a("tr",{key:t.category},[n("td",null,i(t.category),1),n("td",null,i(t.count),1),n("td",null,i(t.percentage)+"%",1)]))),128)),n("tr",null,[e[6]||(e[6]=n("td",null,[n("strong",null,"Total")],-1)),n("td",null,[n("strong",null,i(g.totalCount),1)]),e[7]||(e[7]=n("td",null,[n("strong",null,"100%")],-1))])])])])}]]))}}});
