const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/ngaji-_wmJ0eol.js","assets/vendor-Xnl27S3x.js","assets/ngaji-BuZdky89.css","assets/pantau-ngaji-CxDtEbzK.js","assets/jspdf.es.min-CEEO7WE_.js","assets/jspdf.plugin.autotable-Bc4jgkkc.js","assets/pantau-ngaji-CHzyBH5l.css","assets/pantau-ngaji-cek-Cu1leTKZ.js","assets/pantau-ngaji-cek-DkBQiiLz.css","assets/stat-ngaji-DpVq6Dkb.js","assets/stat-ngaji-C1uXy2v2.css","assets/asrama-BpB0wgKq.js","assets/asrama-DZTi41mk.css","assets/pantau-asrama-BuI7LeXv.js","assets/pantau-asrama-CYCMjRXa.css","assets/stat-asrama-CJN3kEYu.js","assets/stat-asrama-D45LV3sf.css","assets/util-CSPTAgEl.js","assets/util-DYuvGk8T.css"])))=>i.map(i=>d[i]);
import{d as P,c as f,o as _,a as E,b as y,w as L,r as b,e as O,f as j,g as w,h as S,i as N,j as R}from"./vendor-Xnl27S3x.js";function K(){import.meta.url,import("_").catch(()=>1),async function*(){}().next()}(function(){const o=document.createElement("link").relList;if(o&&o.supports&&o.supports("modulepreload"))return;for(const e of document.querySelectorAll('link[rel="modulepreload"]'))l(e);new MutationObserver(e=>{for(const t of e)if(t.type==="childList")for(const n of t.addedNodes)n.tagName==="LINK"&&n.rel==="modulepreload"&&l(n)}).observe(document,{childList:!0,subtree:!0});function s(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),e.crossOrigin==="use-credentials"?t.credentials="include":e.crossOrigin==="anonymous"?t.credentials="omit":t.credentials="same-origin",t}function l(e){if(e.ep)return;e.ep=!0;const t=s(e);fetch(e.href,t)}})();const k=(r,o)=>{const s=r.__vccOpts||r;for(const[l,e]of o)s[l]=e;return s},C=P({data(){return{isLoading:!1}},computed:{currentRoute(){return this.$route}},methods:{setLoading(r){this.isLoading=r}}}),V={class:"app"},D={class:"main",role:"main"},I={key:1,class:"loading"};function T(r,o,s,l,e,t){const n=b("router-view");return _(),f("div",V,[E("main",D,[y(n,null,{default:L(({Component:a,route:p})=>[(_(),f("div",{class:"view-wrapper",key:p.path},[r.isLoading?(_(),f("div",I,"Loading...")):(_(),O(j(a),{key:0,onLoading:r.setLoading},null,40,["onLoading"]))]))]),_:1})])])}const $=k(C,[["render",T]]),U="modulepreload",B=function(r){return"/"+r},g={},u=function(o,s,l){let e=Promise.resolve();if(s&&s.length>0){let n=function(i){return Promise.all(i.map(m=>Promise.resolve(m).then(d=>({status:"fulfilled",value:d}),d=>({status:"rejected",reason:d}))))};document.getElementsByTagName("link");const a=document.querySelector("meta[property=csp-nonce]"),p=(a==null?void 0:a.nonce)||(a==null?void 0:a.getAttribute("nonce"));e=n(s.map(i=>{if(i=B(i),i in g)return;g[i]=!0;const m=i.endsWith(".css"),d=m?'[rel="stylesheet"]':"";if(document.querySelector('link[href="'.concat(i,'"]').concat(d)))return;const c=document.createElement("link");if(c.rel=m?"stylesheet":U,m||(c.as="script"),c.crossOrigin="",c.href=i,p&&c.setAttribute("nonce",p),document.head.appendChild(c),m)return new Promise((A,v)=>{c.addEventListener("load",A),c.addEventListener("error",()=>v(new Error("Unable to preload CSS for ".concat(i))))})}))}function t(n){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=n,window.dispatchEvent(a),!a.defaultPrevented)throw n}return e.then(n=>{for(const a of n||[])a.status==="rejected"&&t(a.reason);return o().catch(t)})},q={Ngaji:()=>u(()=>import("./ngaji-_wmJ0eol.js"),__vite__mapDeps([0,1,2])),PantauNgaji:()=>u(()=>import("./pantau-ngaji-CxDtEbzK.js"),__vite__mapDeps([3,4,5,1,6])),PantauNgajiCek:()=>u(()=>import("./pantau-ngaji-cek-Cu1leTKZ.js"),__vite__mapDeps([7,4,5,1,8])),StatNgaji:()=>u(()=>import("./stat-ngaji-DpVq6Dkb.js"),__vite__mapDeps([9,1,10])),Asrama:()=>u(()=>import("./asrama-BpB0wgKq.js"),__vite__mapDeps([11,1,12])),PantauAsrama:()=>u(()=>import("./pantau-asrama-BuI7LeXv.js"),__vite__mapDeps([13,4,5,1,14])),StatAsrama:()=>u(()=>import("./stat-asrama-CJN3kEYu.js"),__vite__mapDeps([15,1,16])),Util:()=>u(()=>import("./util-CSPTAgEl.js"),__vite__mapDeps([17,1,18]))},x=[{path:"/ngaji",name:"Ngaji",title:"Absensi Acara"},{path:"/pantau-ngaji",name:"PantauNgaji",title:"Pantauan Absensi Acara"},{path:"/pantau-ngaji-cek",name:"PantauNgajiCek",title:"Pantauan Absen Acara"},{path:"/stat-ngaji",name:"StatNgaji",title:"Statistik Absensi Acara"},{path:"/asrama",name:"Asrama",title:"Absensi Asramaan"},{path:"/pantau-asrama",name:"PantauAsrama",title:"Pantauan Absensi Asramaan"},{path:"/stat-asrama",name:"StatAsrama",title:"Statistik Absensi Asramaan"},{path:"/",name:"Util",title:"Utilitas"}],W=x.map(r=>{const{name:o,path:s}=r;return{name:o,path:s,component:q[o],meta:{title:r.title,requiresAuth:!1}}}),F=w({history:S(),routes:W}),h=N($),H=R();h.use(H);h.use(F);h.mount("#app");export{k as _,K as __vite_legacy_guard,u as a};
