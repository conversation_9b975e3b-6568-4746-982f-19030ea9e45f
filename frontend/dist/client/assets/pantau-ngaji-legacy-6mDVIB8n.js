System.register(["./jspdf.es.min-legacy-BqhO63q3.js","./jspdf.plugin.autotable-legacy-fe79WZ9r.js","./vendor-legacy-BmDVBcfe.js","./index-legacy-DRucldFb.js"],function(t,a){"use strict";var e,i,n,r,l,o,s,d,h,p,c,m,g;return{setters:[t=>{e=t.E},null,t=>{i=t.c,n=t.a,r=t.t,l=t.m,o=t.s,s=t.F,d=t.p,h=t.v,p=t.q,c=t.k,m=t.o},t=>{g=t._}],execute:function(){var a=document.createElement("style");a.textContent="body{font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Open Sans,Helvetica Neue,sans-serif}h1{font-size:1.75em;text-align:center;margin-bottom:20px}.filter-item{display:flex;flex-direction:column;gap:15px;background-color:#fff;padding:20px;box-shadow:0 4px 8px rgba(0,0,0,.1);border-radius:20px;margin-bottom:20px;max-width:550px;width:100%;margin-left:auto;margin-right:auto}.filter-item div{display:flex;flex-direction:column}.filter-item label{margin-bottom:5px;font-weight:700}select,input,button{border:1px solid #ccc;padding:10px;font-size:16px;background-color:#f9f9f9;-webkit-appearance:none;appearance:none;border-radius:20px;box-sizing:border-box;width:100%;height:45px;min-height:44px;touch-action:manipulation}select,input{margin-top:0;margin-bottom:0}button{width:auto;margin:20px 10px;padding:0 20px;display:inline-block;cursor:pointer;height:50px}.table-container{width:100%;max-width:768px;overflow-x:auto;border:1px solid #ddd;border-radius:10px;margin:0 auto;-webkit-overflow-scrolling:touch;scrollbar-width:thin}table{width:100%;border-collapse:collapse;table-layout:auto;min-width:100%}th,td{padding:15px;text-align:left;border-bottom:1px solid #ddd;white-space:nowrap}th{background-color:#f4f4f4;cursor:pointer;position:relative;user-select:none;transition:background-color .2s}th:hover{background-color:#e0e0e0}th span{margin-left:5px;color:#666}th:first-child,th:nth-child(2){position:sticky;left:0;background-color:#fff;z-index:20;box-shadow:2px 0 4px rgba(0,0,0,.1)}td:first-child,td:nth-child(2){position:sticky;left:0;background-color:#fff;z-index:10;box-shadow:2px 0 4px rgba(0,0,0,.1)}tr:last-child td{border-bottom:none}th:first-child{cursor:default}th:first-child:hover{background-color:#f4f4f4}.button-container{text-align:center;margin-top:20px;width:100%;max-width:550px;margin-left:auto;margin-right:auto}@media (max-width: 768px){.filter-item{flex-direction:column;max-width:100%}button{width:100%}}@media (max-width: 480px){body{margin:10px}h1{font-size:2em;margin-bottom:15px}.filter-item{padding:15px;gap:12px;margin-bottom:15px}.table-container{font-size:14px}th,td{padding:8px 6px;font-size:13px}th:first-child,td:first-child{width:40px;min-width:40px}button{height:48px;font-size:16px;margin:10px 5px}.button-container{margin-top:15px}}@media (max-width: 414px){h1{font-size:1.8em}.filter-item{padding:12px;border-radius:15px}select,input{height:48px;font-size:16px;padding:12px}.table-container{font-size:13px;border-radius:8px}th,td{padding:6px 4px;font-size:12px}th:first-child,td:first-child{width:35px;min-width:35px}th:nth-child(2),td:nth-child(2){min-width:120px}th:nth-child(3),td:nth-child(3){min-width:80px}th:nth-child(4),td:nth-child(4){min-width:100px}th:nth-child(5),td:nth-child(5){min-width:90px}button{height:50px;margin:8px 3px;border-radius:15px}}@media (max-width: 375px){h1{font-size:1.6em;margin-bottom:10px}.filter-item{padding:10px;gap:10px}.filter-item label{font-size:14px}select,input{height:46px;font-size:15px;padding:10px}.table-container{font-size:12px}th,td{padding:5px 3px;font-size:11px}th:first-child,td:first-child{width:30px;min-width:30px}th:nth-child(2),td:nth-child(2){min-width:100px}th:nth-child(3),td:nth-child(3){min-width:70px}th:nth-child(4),td:nth-child(4){min-width:85px}th:nth-child(5),td:nth-child(5){min-width:80px}button{height:48px;font-size:15px;margin:6px 2px}.button-container{padding:0 5px}}section{width:100%;display:flex;justify-content:center}\n/*$vite$:1*/",document.head.appendChild(a);const f={id:"app"},u={style:{"margin-bottom":"15px","font-weight":"bold","text-align":"center"}},x={class:"filter-item"},b=["value"],w=["value"],y={class:"table-container"},k={id:"attendanceTable"},D={key:0},S={key:0},v={key:0},z={key:0},F={style:{display:"none"}},$={class:"button-container"};t("default",g({data:()=>({attendanceData:[],filters:{ranah:"",kelompok:"",nama:"",tanggal:"",acara:"",lokasi:""},apiKey:"",sortKey:"",sortOrder:"asc"}),computed:{uniqueSesi(){return[...new Set(this.attendanceData.map(t=>t.sesi))].sort()},uniqueRanah(){return[...new Set(this.attendanceData.map(t=>t.ranah))].sort()},availableKelompok(){return this.filters.ranah?[...new Set(this.attendanceData.filter(t=>t.ranah===this.filters.ranah).map(t=>t.detail_ranah))].sort():[...new Set(this.attendanceData.map(t=>t.detail_ranah))].sort()},filteredData(){const t=this.attendanceData.filter(t=>{const a=!this.filters.sesi||t.sesi===this.filters.sesi,e=!this.filters.ranah||t.ranah===this.filters.ranah,i=!this.filters.kelompok||t.detail_ranah===this.filters.kelompok,n=!this.filters.nama||t.nama.toLowerCase().includes(this.filters.nama.toLowerCase());return a&&e&&i&&n});return this.sortKey&&t.sort((t,a)=>{let e="index"===this.sortKey?1:t[this.sortKey],i="index"===this.sortKey?1:a[this.sortKey];return"string"==typeof e&&(e=e.toLowerCase()),"string"==typeof i&&(i=i.toLowerCase()),e<i?"asc"===this.sortOrder?-1:1:e>i?"asc"===this.sortOrder?1:-1:0}),t}},methods:{filterTable(){},onDesaChange(){this.filters.kelompok="",this.filterTable()},formatDateIndonesian(t){const a=new Date(t);return`${["Minggu","Senin","Selasa","Rabu","Kamis","Jumat","Sabtu"][a.getDay()]}, ${a.getDate()} ${["Januari","Februari","Maret","April","Mei","Juni","Juli","Agustus","September","Oktober","November","Desember"][a.getMonth()]} ${a.getFullYear()}`},async fetchDataForDate(){const t=`/api/absen-pengajian/?tanggal=${this.filters.tanggal}&acara=${this.filters.acara}&lokasi=${this.filters.lokasi||""}`;try{const a=await fetch(t,{headers:{Authorization:`ApiKey ${this.apiKey}`}});if(!a.ok)throw new Error(`HTTP error! status: ${a.status}`);this.attendanceData=await a.json()}catch(a){console.error("Error fetching data:",a)}},downloadPDF(){const t=new e({unit:"cm",format:"a4",margins:{top:1,bottom:1,left:1,right:1}});t.setFont("times","normal"),t.setFontSize(20),t.text("Laporan Kehadiran Acara",t.internal.pageSize.getWidth()/2,2,{align:"center"}),t.setFontSize(16),t.text(`${this.filters.acara} - ${this.filters.ranah||"Semua"}`,t.internal.pageSize.getWidth()/2,2.8,{align:"center"}),t.text(`${this.formatDateIndonesian(this.filters.tanggal)}`,t.internal.pageSize.getWidth()/2,3.6,{align:"center"}),t.autoTable({head:[["No.","Nama","Kelompok","Jam Hadir"]],body:this.filteredData.map((t,a)=>[a+1,t.nama,t.detail_ranah,t.jam_hadir]),startY:4.5,margin:{top:1,right:1,left:1,bottom:2},styles:{fontSize:10,cellPadding:.5},pageBreak:"auto",bodyStyles:{minCellHeight:.5},columnStyles:{0:{cellWidth:2},3:{cellWidth:3}},didDrawPage:a=>{const e=a.pageNumber,i=t.internal.pageSize.height,n=t.internal.pageSize.width;t.setFontSize(10);const r=i-1.5;t.setDrawColor(200,200,200),t.setLineWidth(.02),t.line(1,r,n-1,r);const l=this.formatDateIndonesian(this.filters.tanggal),o=`ABSENSI ${this.filters.acara} - ${this.filters.ranah} - ${l} - Halaman ${e}`;t.text(o,n-1,r+.5,{align:"right"})}});const a=`Laporan-Absen-${this.filters.ranah||"semua"}-${this.filters.acara||"UMUM"}-${this.filters.tanggal||"semua"}.pdf`;t.save(a)},sortTable(t){this.sortKey===t?this.sortOrder="asc"===this.sortOrder?"desc":"asc":(this.sortKey=t,this.sortOrder="asc")},openStatistics(){const t=`stat-ngaji.html?key=${this.apiKey}&acara=${this.filters.acara}&lokasi=${this.filters.lokasi}`;window.open(t,"_blank")}},watch:{"filters.acara"(t){document.title=`Pantauan Kehadiran Acara - ${t||"UMUM"}`}},mounted(){const t=new URLSearchParams(window.location.search),a=new Date,e=`${a.getFullYear()}-${String(a.getMonth()+1).padStart(2,"0")}-${String(a.getDate()).padStart(2,"0")}`;this.apiKey=t.get("key"),this.filters.acara=t.get("acara")||"",this.filters.lokasi=t.get("lokasi")||"",this.filters.tanggal=t.get("tanggal")||e,this.fetchDataForDate(),document.title=`Pantauan Kehadiran Acara - ${this.filters.acara||"UMUM"}`}},[["render",function(t,a,e,g,K,C){return m(),i("div",f,[a[26]||(a[26]=n("h1",null,"Pantauan",-1)),n("div",u,r(K.filters.acara||"UMUM"),1),n("section",null,[n("div",x,[n("div",null,[a[15]||(a[15]=n("label",{for:"desaFilter"},"Filter Desa:",-1)),l(n("select",{"onUpdate:modelValue":a[0]||(a[0]=t=>K.filters.ranah=t),onChange:a[1]||(a[1]=(...t)=>C.onDesaChange&&C.onDesaChange(...t)),id:"desaFilter"},[a[14]||(a[14]=n("option",{value:""},"Semua",-1)),(m(!0),i(s,null,d(C.uniqueRanah,t=>(m(),i("option",{key:t,value:t},r(t),9,b))),128))],544),[[o,K.filters.ranah]])]),n("div",null,[a[17]||(a[17]=n("label",{for:"kelompokFilter"},"Filter Kelompok:",-1)),l(n("select",{"onUpdate:modelValue":a[2]||(a[2]=t=>K.filters.kelompok=t),onChange:a[3]||(a[3]=(...t)=>C.filterTable&&C.filterTable(...t)),id:"kelompokFilter"},[a[16]||(a[16]=n("option",{value:""},"Semua",-1)),(m(!0),i(s,null,d(C.availableKelompok,t=>(m(),i("option",{key:t,value:t},r(t),9,w))),128))],544),[[o,K.filters.kelompok]])]),n("div",null,[a[18]||(a[18]=n("label",{for:"tanggalFilter"},"Filter Tanggal:",-1)),l(n("input",{type:"date","onUpdate:modelValue":a[4]||(a[4]=t=>K.filters.tanggal=t),onInput:a[5]||(a[5]=(...t)=>C.fetchDataForDate&&C.fetchDataForDate(...t)),id:"tanggalFilter"},null,544),[[h,K.filters.tanggal]])]),n("div",null,[a[19]||(a[19]=n("label",{for:"namaFilter"},"Filter Nama:",-1)),l(n("input",{type:"text","onUpdate:modelValue":a[6]||(a[6]=t=>K.filters.nama=t),onInput:a[7]||(a[7]=(...t)=>C.filterTable&&C.filterTable(...t)),placeholder:"Cari nama...",id:"namaFilter"},null,544),[[h,K.filters.nama]])])])]),n("div",y,[n("table",k,[n("thead",null,[n("tr",null,[a[24]||(a[24]=n("th",null,"No.",-1)),n("th",{onClick:a[8]||(a[8]=t=>C.sortTable("nama"))},[a[20]||(a[20]=p(" Nama ",-1)),"nama"===K.sortKey?(m(),i("span",D,r("asc"===K.sortOrder?"↑":"↓"),1)):c("",!0)]),n("th",{onClick:a[9]||(a[9]=t=>C.sortTable("ranah"))},[a[21]||(a[21]=p(" Desa ",-1)),"ranah"===K.sortKey?(m(),i("span",S,r("asc"===K.sortOrder?"↑":"↓"),1)):c("",!0)]),n("th",{onClick:a[10]||(a[10]=t=>C.sortTable("detail_ranah"))},[a[22]||(a[22]=p(" Kelompok ",-1)),"detail_ranah"===K.sortKey?(m(),i("span",v,r("asc"===K.sortOrder?"↑":"↓"),1)):c("",!0)]),n("th",{onClick:a[11]||(a[11]=t=>C.sortTable("jam_hadir"))},[a[23]||(a[23]=p(" Jam Hadir ",-1)),"jam_hadir"===K.sortKey?(m(),i("span",z,r("asc"===K.sortOrder?"↑":"↓"),1)):c("",!0)]),a[25]||(a[25]=n("th",{style:{display:"none"}},"Tanggal",-1))])]),n("tbody",null,[(m(!0),i(s,null,d(C.filteredData,(t,a)=>(m(),i("tr",{key:a},[n("td",null,r(a+1),1),n("td",null,r(t.nama),1),n("td",null,r(t.ranah),1),n("td",null,r(t.detail_ranah),1),n("td",null,r(t.jam_hadir),1),n("td",F,r(t.tanggal),1)]))),128))])])]),n("div",$,[n("button",{onClick:a[12]||(a[12]=(...t)=>C.downloadPDF&&C.downloadPDF(...t))},"Download as PDF"),n("button",{onClick:a[13]||(a[13]=(...t)=>C.openStatistics&&C.openStatistics(...t))},"View Statistics")])])}]]))}}});
