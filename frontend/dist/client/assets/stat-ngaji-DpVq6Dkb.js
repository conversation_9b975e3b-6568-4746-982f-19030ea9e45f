import{c as h,a,t as c,m,v as f,F as D,p as y,o as u}from"./vendor-Xnl27S3x.js";import{_ as C}from"./index-Br_OsRMh.js";let d=null,o=null;const T=async()=>(d||await new Promise(t=>{const e=document.createElement("script");e.src="https://cdn.jsdelivr.net/npm/chart.js@3.8.0",e.onload=()=>{d=window.Chart,d.register(d.Title,d.<PERSON>,d.<PERSON>,d.ArcElement,d.CategoryScale,d.LinearScale),t()},document.head.appendChild(e)}),d),w={data(){return{selectedDate:"",referenceTime:new URLSearchParams(window.location.search).get("time")||"08:55",statistics:[],chart:null,fetchedData:[],acara:"",isLoading:!1}},computed:{totalCount(){return this.statistics.reduce((t,e)=>t+Number(e.count),0)}},watch:{referenceTime:{handler(t){var e;(e=this.fetchedData)!=null&&e.length&&requestAnimationFrame(()=>{this.processData(this.fetchedData)})},immediate:!1},acara:{handler(t){document.title=t?"Statistik Kehadiran Acara - ".concat(t):"Statistik Kehadiran Acara"},immediate:!0},selectedDate:{handler(t){t&&(console.log("Date changed to:",t),o&&(o.destroy(),o=null),this.fetchedData=[],this.statistics=[],this.fetchData())},immediate:!1}},methods:{async fetchData(){if(this.isLoading){console.log("Request in progress, skipping new request");return}try{this.isLoading=!0,console.log("Fetching data for date:",this.selectedDate);const t=new URLSearchParams(window.location.search),e=t.get("key");this.acara=t.get("acara")||"";const s=t.get("lokasi")||"",n="/api/absen-pengajian/?tanggal=".concat(this.selectedDate,"&acara=").concat(encodeURIComponent(this.acara),"&lokasi=").concat(encodeURIComponent(s));console.log("Requesting URL:",n);const r=await fetch(n,{headers:{Authorization:"ApiKey ".concat(e),Accept:"application/json"}});if(!r.ok)throw new Error("HTTP error! status: ".concat(r.status));const l=await r.json();if(!Array.isArray(l)){console.error("Invalid data format received:",l),this.handleNoData();return}this.fetchedData=l,this.processData(this.fetchedData)}catch(t){console.error("Error fetching data:",t),this.handleNoData(),console.warn("Failed to fetch attendance data: ".concat(t.message))}finally{this.isLoading=!1}},onReferenceTimeChange(){var t;console.log("Reference time changed to:",this.referenceTime),(t=this.fetchedData)!=null&&t.length&&(o&&(o.destroy(),o=null),this.processData(this.fetchedData))},categorizeAttendance(t){if(!t)return"Unknown";const[e,s]=t.split(":").map(Number);if(Number.isNaN(e)||Number.isNaN(s))return"Unknown";const n=e*60+s,[r,l]=this.referenceTime.split(":").map(Number),i=r*60+l;return n<i?"In Time":n<=i+15?"On Time":"Terlambat"},initializeChart(t){var e;t.classList.add("chart-canvas"),(e=t.parentElement)==null||e.classList.add("chart-container")},async updateChart(t){try{const e=this.$refs.timePieChart;if(!e){console.error("Canvas element not found");return}e.classList.contains("chart-canvas")||this.initializeChart(e);const s=await T(),n=Object.keys(t),r=Object.values(t),l={"In Time":"#82EE84","On Time":"#36A2EB",Terlambat:"#FF2323",Unknown:"#CCCCCC"},i=n.map(p=>l[p]||"#CCCCCC");if(o){o.data.labels=n,o.data.datasets[0].data=r,o.update("none");return}const g={type:"pie",data:{labels:n,datasets:[{data:r,backgroundColor:i}]},options:{responsive:!0,maintainAspectRatio:!0,animation:{duration:300},plugins:{legend:{position:"bottom",labels:{boxWidth:20,padding:10}},title:{display:!0,text:"Distribusi Kehadiran Berdasarkan Waktu",padding:10}}}};o=new s(e,g)}catch(e){console.error("Error updating chart:",e),this.handleChartError(e)}},handleChartError(t){o&&(o.destroy(),o=null),console.error("Chart error:",t.message)},handleNoData(){o&&(o.destroy(),o=null),this.statistics=[],this.fetchedData=[],console.warn("No attendance data available for the selected date.")},processData(t){if(!Array.isArray(t)||!t.length){console.error("Invalid or empty data format:",t),this.handleNoData();return}try{const e=t.reduce((s,n)=>{if(!(n!=null&&n.jam_hadir))return s;const r=this.categorizeAttendance(n.jam_hadir);return r&&(s[r]=(s[r]||0)+1),s},{});if(Object.keys(e).length===0){this.handleNoData();return}requestAnimationFrame(()=>{this.updateChart(e),this.updateStatistics(e)})}catch(e){console.error("Error processing data:",e),this.handleNoData()}},updateStatistics(t){const e=Object.values(t).reduce((s,n)=>s+Number(n),0);this.statistics=Object.entries(t).map(([s,n])=>({category:s,count:n,percentage:(n/e*100).toFixed(1)}))},getTodayDate(){const t=new Date;return"".concat(t.getFullYear(),"-").concat(String(t.getMonth()+1).padStart(2,"0"),"-").concat(String(t.getDate()).padStart(2,"0"))}},async mounted(){try{const e=new URLSearchParams(window.location.search).get("tanggal");if(e)if(/^\d{4}-\d{2}-\d{2}$/.test(e)){const r=new Date(e);!isNaN(r.getTime())&&e===r.toISOString().split("T")[0]?this.selectedDate=e:(console.warn("Invalid date parameter: ".concat(e,", using today's date")),this.selectedDate=this.getTodayDate())}else console.warn("Invalid date format: ".concat(e,", expected yyyy-mm-dd, using today's date")),this.selectedDate=this.getTodayDate();else this.selectedDate=this.getTodayDate();await this.$nextTick();const s=this.$refs.dateInput;s&&s.addEventListener("change",n=>{this.selectedDate=n.target.value}),await this.fetchData()}catch(t){console.error("Error in mounted hook:",t),this.handleNoData()}}},b={id:"pieChartSection"},k={class:"mb-15"},v={class:"mb-10"},N={class:"mb-10"},S={ref:"timePieChart",width:"150",height:"150"},R={id:"statisticsTable"};function j(t,e,s,n,r,l){return u(),h("div",b,[e[9]||(e[9]=a("h2",null,"Statistik",-1)),a("div",k,c(r.acara||"UMUM"),1),a("div",v,[e[4]||(e[4]=a("label",{for:"tanggalFilter"},"Tanggal:",-1)),m(a("input",{type:"date",ref:"dateInput","onUpdate:modelValue":e[0]||(e[0]=i=>r.selectedDate=i),onChange:e[1]||(e[1]=(...i)=>l.fetchData&&l.fetchData(...i))},null,544),[[f,r.selectedDate]])]),a("div",N,[e[5]||(e[5]=a("label",{for:"referenceTime"},"Waktu Referensi:",-1)),m(a("input",{type:"time",id:"referenceTime","onUpdate:modelValue":e[2]||(e[2]=i=>r.referenceTime=i),onChange:e[3]||(e[3]=(...i)=>l.onReferenceTimeChange&&l.onReferenceTimeChange(...i))},null,544),[[f,r.referenceTime]])]),a("canvas",S,null,512),a("table",R,[e[8]||(e[8]=a("thead",null,[a("tr",null,[a("th",null,"Kehadiran"),a("th",null,"Jumlah"),a("th",null,"Persentase")])],-1)),a("tbody",null,[(u(!0),h(D,null,y(r.statistics,i=>(u(),h("tr",{key:i.category},[a("td",null,c(i.category),1),a("td",null,c(i.count),1),a("td",null,c(i.percentage)+"%",1)]))),128)),a("tr",null,[e[6]||(e[6]=a("td",null,[a("strong",null,"Total")],-1)),a("td",null,[a("strong",null,c(l.totalCount),1)]),e[7]||(e[7]=a("td",null,[a("strong",null,"100%")],-1))])])])])}const L=C(w,[["render",j]]);export{L as default};
