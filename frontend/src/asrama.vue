<template>
    <div class="page-wrapper">
        <div class="content-area">
            <div class="form-container" :class="{ hidden: showSuccess }">
                <div
                    class="event-title"
                    v-html="displayAcara || 'ASRAMA'"
                ></div>
                <div class="form-title">ABSENSI</div>
                <div class="event-date" v-if="formData.tanggal">
                    {{ formatDate(formData.tanggal) }}
                </div>
                <form @submit.prevent="submitForm">
                    <select
                        v-model="formData.sesi"
                        required
                        :disabled="!sesiOptions.length"
                    >
                        <option value="" disabled selected>SESI</option>
                        <option
                            v-for="sesi in sesiOptions"
                            :key="sesi"
                            :value="sesi"
                        >
                            {{ sesi }}
                        </option>
                    </select>
                    <input
                        type="text"
                        v-model="formData.nama"
                        placeholder="NAMA"
                        required
                    />
                    <div class="suggestions-container">
                        <input
                            type="text"
                            v-model="kelompokInput"
                            @input="handleKelompokInput"
                            @keyup="handleKelompokKeyup"
                            @compositionend="handleCompositionEnd"
                            @focus="handleKelompokFocus"
                            @blur="handleKelompokBlur"
                            ref="kelompokInputEl"
                            style="touch-action: manipulation"
                            :placeholder="placeholderText"
                            required
                        />
                        <div class="suggestions-wrapper">
                            <div
                                class="suggestions"
                                v-if="
                                    showSuggestions &&
                                    kelompokInput &&
                                    filteredKelompok.length
                                "
                            >
                                <div
                                    v-for="item in filteredKelompok"
                                    :key="`${item.kelompok}-${item.desa}`"
                                    class="suggestion-item"
                                    @click.stop="selectKelompok(item)"
                                >
                                    {{ item.kelompok }} ({{ item.desa }})
                                </div>
                            </div>
                        </div>
                    </div>
                    <input type="hidden" v-model="formData.detail_ranah" />
                    <input type="hidden" v-model="formData.ranah" />
                    <input type="hidden" v-model="formData.jam_hadir" />
                    <input type="hidden" v-model="formData.lokasi" />
                    <input type="hidden" v-model="formData.tanggal" />
                    <button type="submit">Kirim Data</button>
                </form>
            </div>

            <!-- Success message -->
            <div v-if="showSuccess" class="form-container">
                <div class="confirmation-message">
                    DATA ABSEN ANDA<br />SUDAH KAMI TERIMA.<br /><br />Alhamdulillah<br />Jazaa
                    Kumullohu Khoiro.
                </div>
                <button @click="resetForm">Kembali</button>
            </div>
        </div>

        <!-- Warning message at the bottom -->
        <div class="footer-area">
            <div class="warning-container">
                WARNING!!!<br />DILARANG mengoperasikan HP<br />selama acara
                berlangsung.
            </div>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            formData: {
                nama: "",
                ranah: "",
                detail_ranah: "",
                jam_hadir: "",
                tanggal: "",
                acara: "", // Raw value from URL
                lokasi: "", // Will be populated from URL
                sesi: "", // For sesi selection
            },
            sesiOptions: [], // Will store sesi data from API
            showSuccess: false,
            kelompokInput: "",
            previousKelompokInput: "", // Add this to track previous input
            showSuggestions: false, // Changed to false by default
            kelompokOptions: {}, // Initialize empty, will be populated from API
            isLoading: true, // Add loading state
            loadError: null, // Track loading errors
            dataLoaded: false, // Track if API data is loaded
            placeholderText: "KELOMPOK (DESA) / DAPUKAN", // Default placeholder
            displayAcara: "", // For display purposes
            isMobileKeyboardVisible: false, // New state for mobile keyboard tracking
            inputTimer: null, // Add timer for debounce
            isComposing: false, // Add composing state for IME
        };
    },
    computed: {
        flattenedKelompok() {
            return Object.entries(this.kelompokOptions).flatMap(
                ([desa, kelompoks]) =>
                    kelompoks.map((kelompok) => ({ desa, kelompok })),
            );
        },
        filteredKelompok() {
            const searchTerm = this.kelompokInput.toLowerCase();
            if (!searchTerm || searchTerm.length < 1) return [];

            if (!this.dataLoaded) {
                console.log("Data not yet loaded, returning empty array");
                return [];
            }

            const filtered = this.flattenedKelompok.filter(
                (item) =>
                    item.kelompok.toLowerCase().includes(searchTerm) ||
                    item.desa.toLowerCase().includes(searchTerm),
            );
            const unique = [];
            const seen = new Set();
            for (const item of filtered) {
                const identifier = `${item.kelompok.toLowerCase()}-${item.desa.toLowerCase()}`;
                if (!seen.has(identifier)) {
                    seen.add(identifier);
                    unique.push(item);
                }
            }
            return unique;
        },
    },
    watch: {
        kelompokInput(newVal) {
            console.log(
                `kelompokInput changed to "${newVal}" (length: ${newVal.length})`,
            );
            // Show suggestions when there's at least 1 character
            const shouldShow = newVal.length >= 1;
            console.log(
                `Setting showSuggestions to ${shouldShow} based on input length`,
            );
            this.showSuggestions = shouldShow;
        },
    },
    methods: {
        formatDate(dateString) {
            const days = [
                "Minggu",
                "Senin",
                "Selasa",
                "Rabu",
                "Kamis",
                "Jumat",
                "Sabtu",
            ];
            const months = [
                "Januari",
                "Februari",
                "Maret",
                "April",
                "Mei",
                "Juni",
                "Juli",
                "Agustus",
                "September",
                "Oktober",
                "November",
                "Desember",
            ];
            const date = new Date(`${dateString}T00:00:00`);
            const jakartaDate = new Date(
                date.toLocaleString("en-US", { timeZone: "Asia/Jakarta" }),
            );
            return `${days[jakartaDate.getDay()]}, ${jakartaDate.getDate()} ${months[jakartaDate.getMonth()]} ${jakartaDate.getFullYear()}`;
        },
        getUrlParameter(name) {
            return new URLSearchParams(window.location.search).get(name) || "";
        },
        handleKelompokInput() {
            console.log(
                `handleKelompokInput called with current input: "${this.kelompokInput}"`,
            );

            // Clear any pending timers to prevent race conditions
            if (this.inputTimer) {
                clearTimeout(this.inputTimer);
            }

            // Use timer to process input after a short delay
            this.inputTimer = setTimeout(() => {
                // If the input is empty but the element has value, synchronize them
                const inputEl = this.$refs.kelompokInputEl;
                if (!this.kelompokInput && inputEl && inputEl.value) {
                    console.log(
                        `Synchronizing input value: "${inputEl.value}"`,
                    );
                    this.kelompokInput = inputEl.value;
                }

                // Check if user is deleting a character
                if (
                    this.kelompokInput.length <
                    this.previousKelompokInput.length
                ) {
                    console.log("Deletion detected, clearing input");
                    // User is deleting - clear the entire input
                    this.kelompokInput = "";
                }

                // Store current value for next comparison
                this.previousKelompokInput = this.kelompokInput;

                // Only show suggestions when there is at least 1 character
                const shouldShow = this.kelompokInput.length >= 1;
                console.log(
                    `Setting showSuggestions to ${shouldShow} based on input length (${this.kelompokInput.length})`,
                );
                this.showSuggestions = shouldShow;

                // Only clear form data if user is typing something new, not after selection
                // Check if input doesn't match the pattern of a selected item "kelompok (desa)"
                if (
                    !this.kelompokInput.includes(" (") ||
                    !this.kelompokInput.includes(")")
                ) {
                    console.log(
                        "New input detected, clearing previous selection",
                    );
                    this.formData.detail_ranah = "";
                    this.formData.ranah = "";
                }

                if (!this.dataLoaded && !this.isLoading) {
                    console.log("Data not loaded, retrying fetch...");
                    this.fetchKelompokData();
                }
            }, 50); // Small delay to ensure we get complete input
        },
        handleKelompokKeyup(_event) {
            // Get input value directly from the DOM element to work around v-model sync issues
            const inputEl = this.$refs.kelompokInputEl;
            if (inputEl && this.kelompokInput !== inputEl.value) {
                console.log(
                    `Keyup detected value mismatch. v-model: "${this.kelompokInput}", element: "${inputEl.value}"`,
                );
                this.kelompokInput = inputEl.value;
                this.handleKelompokInput();
            }
        },
        handleCompositionEnd(event) {
            // For IME input handling (like Chinese, Japanese, Korean keyboards)
            this.isComposing = false;
            console.log(`Composition ended with text: "${event.data}"`);
            this.kelompokInput = event.target.value;
            this.handleKelompokInput();
        },
        handleKelompokFocus() {
            console.log(
                `handleKelompokFocus called with current input: "${this.kelompokInput}"`,
            );
            // Show suggestions when there is at least 1 character
            const shouldShow = this.kelompokInput.length >= 1;
            console.log(
                `Focus event: Setting showSuggestions to ${shouldShow} (input length: ${this.kelompokInput.length})`,
            );
            this.showSuggestions = shouldShow;
        },
        handleKelompokBlur() {
            console.log(
                "handleKelompokBlur called, scheduling suggestion hide",
            );
            // Only hide suggestions if we're not in the middle of a selection
            if (!this._selectionInProgress) {
                setTimeout(() => {
                    console.log("Blur timeout executed, hiding suggestions");
                    this.showSuggestions = false;
                }, 150);
            }
        },
        selectKelompok(item) {
            this._selectionInProgress = true;
            console.log(
                `selectKelompok called with item: ${item.kelompok} (${item.desa})`,
            );
            this.kelompokInput = `${item.kelompok} (${item.desa})`;

            // Update form data values immediately - no delay
            this.formData.detail_ranah = item.kelompok;
            this.formData.ranah = item.desa;

            // Force Vue to recognize the changes
            this.formData = { ...this.formData };

            console.log("Form data updated immediately:", {
                detail_ranah: this.formData.detail_ranah,
                ranah: this.formData.ranah,
            });

            // Hide suggestions after selection for better UX
            setTimeout(() => {
                this.showSuggestions = false;
                this._selectionInProgress = false;

                // Double-check that data is still there
                console.log("Verification after timeout:", {
                    detail_ranah: this.formData.detail_ranah,
                    ranah: this.formData.ranah,
                });
            }, 200);
        },
        validateForm() {
            if (!this.formData.nama.trim()) {
                alert("Nama harus diisi");
                return false;
            }

            if (!this.formData.sesi) {
                alert("Sesi harus dipilih");
                return false;
            }

            if (!this.formData.detail_ranah || !this.formData.ranah) {
                alert("Kelompok harus dipilih dari daftar yang tersedia");
                return false;
            }

            const isValidKelompok = this.flattenedKelompok.some(
                (item) =>
                    item.kelompok.toLowerCase() ===
                        this.formData.detail_ranah.toLowerCase() &&
                    item.desa.toLowerCase() ===
                        this.formData.ranah.toLowerCase(),
            );
            if (!isValidKelompok) {
                alert(
                    "Silahkan pilih kelompok sesuai dengan pilihan yang muncul saat Anda mengetik",
                );
                return false;
            }

            return true;
        },
        async submitForm() {
            if (!this.validateForm()) {
                return;
            }

            const now = new Date();
            const todayTime = new Date(
                now.toLocaleString("en-US", {
                    timeZone: "Asia/Jakarta",
                }),
            );
            this.formData.jam_hadir = `${todayTime.getHours().toString().padStart(2, "0")}:${todayTime.getMinutes().toString().padStart(2, "0")}`;
            this.formData.tanggal = todayTime.toLocaleDateString("en-CA", {
                timeZone: "Asia/Jakarta",
            });

            const apiKey = this.getUrlParameter("key");
            if (!apiKey) {
                alert(
                    "Kunci API tidak ditemukan. Silakan berikan kunci yang valid di URL",
                );
                return;
            }

            try {
                const payload = {
                    ...this.formData,
                    nama: this.formData.nama.trim(),
                    detail_ranah: this.formData.detail_ranah.trim(),
                    ranah: this.formData.ranah.trim(),
                };

                const response = await fetch(
                    "/api/absen-asramaan/",
                    {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json",
                            Authorization: `ApiKey ${apiKey}`,
                        },
                        body: JSON.stringify(payload),
                    },
                );

                let responseData;
                try {
                    const responseText = await response.text();
                    responseData = JSON.parse(responseText);
                } catch (e) {
                    console.error("Failed to parse response as JSON:", e);
                    alert(
                        "Terjadi kesalahan saat memproses respons dari server",
                    );
                    return;
                }

                if (response.status === 422) {
                    const errorMessage = responseData.detail
                        ? Array.isArray(responseData.detail)
                            ? responseData.detail.join("\n")
                            : responseData.detail
                        : "Data yang dikirim tidak valid";
                    alert(errorMessage);
                    return;
                }

                if (response.ok && responseData.id) {
                    this.showSuccess = true;
                } else if (responseData.detail) {
                    const detail = Array.isArray(responseData.detail)
                        ? responseData.detail[0]
                        : responseData.detail;

                    if (
                        typeof detail === "string" &&
                        detail.includes("Duplicate entry detected")
                    ) {
                        alert(
                            "Data yang sama sudah dimasukkan, silakan cek kembali",
                        );
                    } else {
                        console.error("Server error detail:", detail);
                        alert(`Error: ${detail}`);
                    }
                } else {
                    throw new Error("Unexpected response format");
                }
            } catch (error) {
                console.error("Network or system error:", error);
                alert(
                    "Terjadi kesalahan saat mengirim data. Silakan coba lagi",
                );
            }
        },
        resetForm() {
            window.location.reload();
        },
        async fetchKelompokData() {
            console.log("Fetching kelompok data...");
            try {
                const dataParam = this.getUrlParameter("data");
                if (!dataParam) {
                    throw new Error("Parameter data tidak ditemukan di URL");
                }

                const encodedParam = encodeURIComponent(dataParam);
                const response = await fetch(
                    `/api/data/daerah/${encodedParam}/`,
                );
                if (!response.ok) {
                    throw new Error("Network response was not ok");
                }
                const data = await response.json();
                const formattedData = {};

                for (const item of data) {
                    if (!formattedData[item.ranah]) {
                        formattedData[item.ranah] = [];
                    }
                    formattedData[item.ranah].push(item.detail_ranah);
                }

                this.kelompokOptions = formattedData;
                this.isLoading = false;
                this.loadError = null;
                this.dataLoaded = true;
            } catch (error) {
                console.error("Error fetching kelompok data:", error);
                this.loadError =
                    "Gagal memuat data kelompok. Silakan muat ulang halaman.";
                this.isLoading = false;
                this.dataLoaded = false;

                setTimeout(() => {
                    this.fetchKelompokData();
                }, 5000);
            }
        },
        async fetchSesiData() {
            try {
                const sesiParam = this.getUrlParameter("sesi");
                if (!sesiParam) {
                    console.warn("Parameter sesi tidak ditemukan di URL");
                    return;
                }

                const encodedParam = encodeURIComponent(sesiParam);
                const response = await fetch(
                    `/api/data/sesi/${encodedParam}`,
                );

                if (!response.ok) {
                    throw new Error("Network response was not ok");
                }

                const data = await response.json();
                this.sesiOptions = Array.isArray(data)
                    ? data.map((item) => item.sesi)
                    : [];
            } catch (error) {
                console.error("Error fetching sesi data:", error);
                alert("Gagal memuat data sesi. Silakan muat ulang halaman.");
            }
        },
        processDisplayText(text) {
            // Handle both double spaces and explicit newlines
            return text.replace(/[ ]{2}|\n/g, "<br>");
        },
    },
    async mounted() {
        console.log("Component mounted");
        try {
            await fetch(window.location.pathname, {
                cache: "reload",
                credentials: "same-origin",
            });
        } catch (e) {
            console.warn("Cache clearing failed:", e);
        }

        const now = new Date();
        const todayTime = new Date(
            now.toLocaleString("en-US", {
                timeZone: "Asia/Jakarta",
            }),
        );
        this.formData.tanggal = todayTime.toLocaleDateString("en-CA", {
            timeZone: "Asia/Jakarta",
        });

        const rawAcara = this.getUrlParameter("acara");
        this.formData.acara = rawAcara;
        this.displayAcara = this.processDisplayText(rawAcara);

        this.formData.lokasi = this.getUrlParameter("lokasi");
        const customPlaceholder = this.getUrlParameter("ph");
        if (customPlaceholder) {
            this.placeholderText = customPlaceholder;
        }

        document.title = `Absensi Acara - ${rawAcara.replace(/\s{2,}/g, " - ")}`;

        await Promise.all([this.fetchKelompokData(), this.fetchSesiData()]);

        // Add event listener to detect device keyboard issues
        document.addEventListener("touchend", (event) => {
            if (event.target === this.$refs.kelompokInputEl) {
                setTimeout(() => {
                    const inputEl = this.$refs.kelompokInputEl;
                    if (inputEl && this.kelompokInput !== inputEl.value) {
                        console.log(
                            `Touch event detected value mismatch. v-model: "${this.kelompokInput}", element: "${inputEl.value}"`,
                        );
                        this.kelompokInput = inputEl.value;
                        this.handleKelompokInput();
                    }
                }, 100);
            }
        });
    },
};
</script>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
        "Helvetica Neue", Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #ffffff;
    color: #2c4a3e;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.form-container {
    width: 100%;
    max-width: 360px;
    padding: 20px;
    border-radius: 20px;
    background-color: #f9f9f9;
    box-shadow:
        0 10px 25px rgba(44, 74, 62, 0.2),
        0 6px 12px rgba(44, 74, 62, 0.15);
    box-sizing: border-box;
    text-align: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: opacity 0.3s ease-in-out;
}

.hidden {
    opacity: 0;
    pointer-events: none;
}

.form-title {
    font-size: 24px;
    font-weight: bold;
    color: #2e5a35;
    margin-bottom: 12px;
}

input,
select {
    width: 100%;
    padding: 12px;
    margin-bottom: 15px;
    border: 1px solid #2e5a35;
    border-radius: 20px;
    font-size: 16px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
    appearance: none;
    background-color: #ffffff;
    color: #2c4a3e;
}

/* Custom styles for select element */
select {
    padding-right: 40px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%232e5a35' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
    cursor: pointer;
    text-align: left;
}

select:disabled {
    background-color: #f0f0f0;
    cursor: not-allowed;
    opacity: 0.7;
}

input::placeholder {
    color: #2e5a35;
    opacity: 0.7;
}

input:focus,
select:focus {
    border-color: #2e5a35;
    outline: none;
}

button {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 20px;
    background-color: #2e5a35;
    color: #fff;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    box-sizing: border-box;
    margin-top: 10px;
}

button:hover {
    background-color: #3d7a47;
}

.confirmation-message {
    font-size: 20px;
    font-weight: bold;
    color: #2e5a35;
}

.suggestions {
    position: relative;
    width: 100%;
    max-height: 40vh;
    overflow-y: auto;
    background: #ffffff;
    border: 1px solid #2e5a35;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
    margin-top: 5px;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
    z-index: 10000 !important;
}

.suggestion-item {
    padding: 10px;
    cursor: pointer;
    text-align: left;
    color: #2c4a3e;
    touch-action: pan-y;
}

.suggestion-item {
    padding: 10px;
    cursor: pointer;
    text-align: left;
    color: #2c4a3e;
}

.suggestion-item:hover {
    background-color: #e8f3ed;
}

.event-title {
    margin-bottom: 8px;
    font-weight: bold;
    color: #2e5a35;
    font-size: 18px;
    white-space: pre-line;
}

.event-date {
    margin-bottom: 15px;
    color: #2e5a35;
    font-size: 14px;
}

.suggestions-container {
    position: relative;
    z-index: 10000 !important;
    margin-bottom: 25px; /* Add extra margin to make room for suggestions */
}

.suggestions-wrapper {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    z-index: 10000 !important;
}

.warning-container {
    width: 100%;
    max-width: 360px;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(44, 74, 62, 0.1);
    box-sizing: border-box;
    text-align: center;
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    font-weight: bold;
    animation: blinkColors 1s infinite;
    z-index: 100;
}

@keyframes blinkColors {
    0%,
    100% {
        background-color: #cc0000;
        color: #fff;
    }
    50% {
        background-color: #fff;
        color: #cc0000;
    }
}

/* Page structure */
.page-wrapper {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    position: relative;
    padding-bottom: 0; /* Space for the warning container */
    width: 100%;
}

.content-area {
    flex: 1;
    position: relative;
}

.footer-area {
    position: fixed;
    width: 100%;
    margin-top: auto;
    padding-bottom: 5px;
    bottom: 0;
}

/* Form container positioning */
.form-container {
    width: 100%;
    max-width: 360px;
    padding: 20px;
    border-radius: 20px;
    background-color: #f9f9f9;
    box-shadow:
        0 10px 25px rgba(44, 74, 62, 0.2),
        0 6px 12px rgba(44, 74, 62, 0.15);
    box-sizing: border-box;
    text-align: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: opacity 0.3s ease-in-out;
    z-index: 100;
}

/* Warning container - make it static */
.warning-container {
    width: 100%;
    max-width: 360px;
    padding: 15px;
    margin: 0 auto;
    border-radius: 10px;
    background-color: #cc0000;
    color: #fff;
    box-shadow: 0 4px 8px rgba(44, 74, 62, 0.1);
    box-sizing: border-box;
    text-align: center;
    font-weight: bold;
    animation: blinkColors 1s infinite;
    z-index: 50;
    line-height: 1.25;
}
</style>
