// Cloudflare Workers as API proxy for besb project
const API_HOST = "api.var.my.id";

// Whitelist of allowed API endpoint prefixes based on actual backend structure
// These correspond to the FastAPI and Django backend endpoints
const ALLOWED_PATHS = [
  "/admin", // Django admin access
  "/auth", // Authentication endpoints (Django)
  "/absen-pengajian", // Religious study attendance (FastAPI)
  "/absen-asramaan", // Dormitory attendance (FastAPI)
  "/biodata", // Student biodata endpoints (FastAPI)
  "/data", // Data endpoints (FastAPI) - covers /data/daerah, /data/sesi, /data/materi, /data/hobi, /data/kelas-sekolah
  "/url", // URL shortener endpoints (FastAPI)
  "/wilayah", // Area/region endpoints (FastAPI)
];

export default {
  async fetch(request, env) {
    const url = new URL(request.url);

    // Handle shortened URLs: /s/url_id_shortened
    if (url.pathname.startsWith("/s/")) {
      // Extract the shortened URL ID
      const urlId = url.pathname.substring(3); // Remove '/s/' prefix

      if (urlId) {
        try {
          // Proxy to backend URL endpoint to resolve shortened URL
          const targetUrl = `https://${API_HOST}/url/${urlId}${url.search}`;
          const response = await fetch(targetUrl, {
            method: "GET",
            headers: {
              "User-Agent": "Cloudflare-Worker/1.0",
            },
          });

          if (response.ok) {
            const data = await response.json();
            // Redirect to the original URL
            return Response.redirect(data.url, 302);
          } else {
            return new Response("URL not found", { status: 404 });
          }
        } catch (error) {
          console.error("Error fetching short URL:", error);
          return new Response("Internal Server Error", { status: 500 });
        }
      } else {
        return new Response("Bad Request: Missing URL ID", { status: 400 });
      }
    }

    // Handle API proxy requests: /api/*
    if (url.pathname.startsWith("/api/")) {
      // Extract the path after '/api' to check against the whitelist
      const apiPath = url.pathname.substring(4); // e.g., /data/daerah/123

      // Check if the requested path is allowed
      const isAllowed = ALLOWED_PATHS.some((prefix) =>
        apiPath.startsWith(prefix),
      );

      if (isAllowed) {
        // If allowed, proxy the request to backend
        const targetPath = url.pathname.replace(/^\/api\//, "/");
        const targetUrl = `https://${API_HOST}${targetPath}${url.search}`;

        return fetch(targetUrl, {
          method: request.method,
          headers: request.headers,
          body: request.body,
          redirect: "follow",
        });
      } else {
        // If not allowed, block the request
        return new Response("Forbidden: Endpoint not allowed", { status: 403 });
      }
    }

    // For all other requests, serve static assets or SPA
    try {
      // Try to serve static assets using the ASSETS binding
      if (env.ASSETS) {
        const assetResponse = await env.ASSETS.fetch(request);

        // If the asset was found (not a 404), return it
        if (assetResponse.status !== 404) {
          return assetResponse;
        }

        // If asset not found, serve the SPA index.html for client-side routing
        // This handles Vue.js routes like /absok, /pantau, /stat, /util
        const indexUrl = new URL(request.url);
        indexUrl.pathname = "/index.html";
        // Preserve the original query parameters for the SPA
        const indexRequest = new Request(indexUrl.toString(), {
          method: request.method,
          headers: request.headers,
        });
        return env.ASSETS.fetch(indexRequest);
      }
    } catch (error) {
      console.error("Error serving assets:", error);
    }

    // Fallback to regular fetch
    return fetch(request);
  },
};
